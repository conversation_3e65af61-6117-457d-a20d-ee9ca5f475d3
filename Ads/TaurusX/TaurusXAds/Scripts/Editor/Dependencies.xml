<!-- Copyright (C) 2018 Google Inc. All Rights Reserved.

FirebaseApp iOS and Android Dependencies.
-->

<dependencies>

  <!-- iOS -->
  <iosPods>
    <!-- TaurusX -->
    <iosPod name="TaurusXAds" version="~> 1.5.2">
        <sources>
            <source>https://github.com/CocoaPods/Specs</source>
        </sources>
    </iosPod>
        <!-- 穿山甲 -->
        <iosPod name="TaurusXAdMediation_TikTok" version="~> 2.5.1.5.1">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- 广点通 -->
        <iosPod name="TaurusXAdMediation_GDT" version="~> 4.10.19.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- AdColony -->
        <iosPod name="TaurusXAdMediation_AdColony" version="~> 4.1.2.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- AdMob -->
        <iosPod name="TaurusXAdMediation_GoogleAds" version="~> 7.52.0.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- AppLovin -->
        <iosPod name="TaurusXAdMediation_AppLovin" version="~> 6.9.4.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- Chartboost -->
        <iosPod name="TaurusXAdMediation_Chartboost" version="~> 8.0.1.2">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- ironsource -->
        <iosPod name="TaurusXAdMediation_IronSource" version="~> 6.10.0.0.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- Unity Ads -->
        <iosPod name="TaurusXAdMediation_UnityAds" version="~> 3.3.0.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- Vungle -->
        <iosPod name="TaurusXAdMediation_Vungle" version="~> 6.4.5.1">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
        <!-- facebook -->
        <iosPod name="TaurusXAdMediation_Facebook" version="~> 5.5.1.0">
            <sources>
                <source>https://github.com/CocoaPods/Specs</source>
            </sources>
        </iosPod>
  </iosPods>

  




  <androidPackages>

    <!-- TaurusX -->
    <androidPackage spec="com.taurusx.ads:core:1.9.17">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    

    <!-- 广点通 -->
    <androidPackage spec="com.taurusx.ads:mediation_gdt:4.100.970.0">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>

    <!-- 4.80.950 之前的版本，不需要 gdtsdk_tbs 依赖 -->
    <androidPackage spec="com.taurusx.ads:gdtsdk_union:4.100.970">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>

    <androidPackage spec="com.android.support:support-v4:26.1.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- 穿山甲 -->
    <androidPackage spec="com.taurusx.ads:mediation_toutiao:2.5.2.6.2">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.taurusx.ads:toutiao_open_ad_sdk:2.5.2.6">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="pl.droidsonroids.gif:android-gif-drawable:1.2.6">
      <repositories>
        <repository>https://jcenter.bintray.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.android.support:support-v4:27.1.1">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- AdColony -->
    <androidPackage spec="com.taurusx.ads:mediation_adcolony:3.3.7.4">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-ads-identifier:16.0.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-ads:17.1.2">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- AdMob -->
    <androidPackage spec="com.taurusx.ads:mediation_admob:17.2.1.5">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-ads:17.2.1">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- Facebook -->
    <androidPackage spec="com.taurusx.ads:mediation_facebook:5.6.0.0">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.facebook.android:audience-network-sdk:5.6.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    <!-- 5.5.0 及之后的版本需要 support-annotations -->
    <androidPackage spec="com.android.support:support-annotations:28.0.0">
    </androidPackage>

    <!-- IronSource -->
    <androidPackage spec="com.taurusx.ads:mediation_ironsource:6.8.0.1.6">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>

    <androidPackage spec="com.android.support:support-v4:26.1.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <androidPackage spec="com.google.android.gms:play-services-ads-identifier:16.0.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- MoPub -->
    <androidPackage spec="com.taurusx.ads:mediation_mopub:5.8.0.0">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.mopub:mopub-sdk:5.8.0">
      <repositories>
        <repository>https://jcenter.bintray.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-basement:16.1.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    <!-- Tapjoy -->
    <androidPackage spec="com.taurusx.ads:mediation_tapjoy:12.2.0.1">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>

    <androidPackage spec="com.tapjoy:tapjoy-android-sdk:12.2.0@aar">
      <repositories>
        <repository>https://jcenter.bintray.com/</repository>
      </repositories>
    </androidPackage>

    <androidPackage spec="com.google.android.gms:play-services-ads:17.1.2">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- Unity Ads -->
    <androidPackage spec="com.taurusx.ads:mediation_unity:3.1.0.1">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.taurusx.ads:unity-ads:3.1.0">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>

    <!-- Vungle -->
    <androidPackage spec="com.taurusx.ads:mediation_vungle:6.3.24.3">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.github.vungle:vungle-android-sdk:6.3.24">
      <repositories>
        <repository>https://jitpack.io</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-basement:16.1.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-ads-identifier:16.0.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-location:16.0.0">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>

    <!-- AppLovin -->
    <androidPackage spec="com.taurusx.ads:mediation_applovin:9.9.2.1">
      <repositories>
        <repository>https://dl.bintray.com/taurrusxads/TaurusxAds</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.applovin:applovin-sdk:9.9.2">
      <repositories>
        <repository>https://jcenter.bintray.com/</repository>
      </repositories>
    </androidPackage>
    <androidPackage spec="com.google.android.gms:play-services-ads:17.2.1">
      <repositories>
        <repository>https://maven.google.com/</repository>
      </repositories>
    </androidPackage>
    
  </androidPackages>
</dependencies>

