using System;
using System.Collections.Generic;
using TaurusXAdSdk.Api;
using TaurusXAdSdk.Common;

namespace TaurusXAdSdk.Platforms.iOS
{
    public class LineItemClient : ILineItemClient
    {
        private IntPtr mLineItem;

        public LineItemClient(IntPtr lineItem)
        {
            mLineItem = lineItem;
        }

        #region ILineItemClient

        public string GetMediationVersion() {
            return Externs.TXADGetLineItemMediationVersion(mLineItem);
        }

        public string GetName() {
            return Externs.TXADGetLineItemName(mLineItem);
        }

        public AdType GetAdType() {
            return (AdType)Externs.TXADGetLineItemAdType(mLineItem);
        }

        public Network GetNetwork() {
            int networkId = Externs.TXADGetLineItemNetwork(mLineItem);
            return (Network)networkId;
        }

        public int GetPriority() {
            return Externs.TXADGetLineItemPriority(mLineItem);
        }

        public float GetEcpm() {
            return Externs.TXADGetLineItemEcpm(mLineItem);
        }

        // ms
        public int GetRequestTimeOut() {
            return Externs.TXADGetLineItemRequestTimeOut(mLineItem);
        }

        public bool IsHeaderBidding() {
            return Externs.TXADGetLineItemIsHeaderBidding(mLineItem);
        }

        // ms
        public int GetHeaderBiddingTimeOut() {
            return Externs.TXADGetLineItemHeaderBiddingTimeOut(mLineItem);
        }

        public Dictionary<string, string> GetServerExtras() {
            Dictionary<string, string> dictionary = new Dictionary<string, string>();

            string serverExtras = Externs.TXADGetLineItemServerExtras(mLineItem);
            JSONObject serverExtrasObject = (JSONObject)JSONNode.Parse(serverExtras);
            foreach (KeyValuePair<string, JSONNode> kv in serverExtrasObject) {
                dictionary.Add(kv.Key, kv.Value);
            }

            return dictionary;
        }

        public string GetNetworkAdUnitId()
        {
            return Externs.TXADGetLineItemNetworkAdUnitId(mLineItem);
        }

        // Belonged AdUnit
        public AdUnit GetAdUnit() {
            IntPtr adUnit = Externs.TXADGetLineItemAdUnit(mLineItem);
            return new AdUnit(new AdUnitClient(adUnit));
        }

        public SecondaryLineItem GetSecondaryLineItem() {
            IntPtr secondaryLineItem = Externs.TXADGetLineItemSecondaryLineItem(mLineItem);
            return new SecondaryLineItem(new SecondaryLineItemClient(secondaryLineItem));
        }

        public string GetTId() {
            return Externs.TXADGetLineItemTId(mLineItem);
        }

        #endregion
    }
}
