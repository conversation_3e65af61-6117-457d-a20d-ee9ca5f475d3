using System;
using System.Collections.Generic;
using TaurusXAdSdk.Api;
using TaurusXAdSdk.Common;

namespace TaurusXAdSdk.Platforms.iOS
{
    public class SecondaryLineItemClient : ISecondaryLineItemClient
    {
        private IntPtr mSecondaryLineItemClient;

        public SecondaryLineItemClient(IntPtr secondaryLineItem)
        {
            mSecondaryLineItemClient = secondaryLineItem;
        }

        #region ISecondaryLineItemClient

        public Network GetNetwork() {
            int networkId = Externs.TXADGetSecondaryLineItemNetwork(mSecondaryLineItemClient);
            return (Network)networkId;
        }

        public float GetEcpm() {
            return Externs.TXADGetSecondaryLineItemEcpm(mSecondaryLineItemClient);
        }

        public string GetAdUnitId()
        {
            return Externs.TXADGetSecondaryLineItemAdUnitId(mSecondaryLineItemClient);
        }

        #endregion
    }
}