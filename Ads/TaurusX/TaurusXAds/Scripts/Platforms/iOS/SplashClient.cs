using System;
using TaurusXAdSdk.Api;
using TaurusXAdSdk.Common;
using System.Runtime.InteropServices;

// Deprecated: Directly use iOS TXADSplashAd in Xcode project's AppController.
namespace TaurusXAdSdk.Platforms.iOS
{
    public class SplashClient : ISplashClient, IDisposable
    {
        private IntPtr mSplashAdPtr;
        private IntPtr mSplashClientPtr;

        // This property should be used when setting the mInterstitialAdPtr.
        private IntPtr SplashAdPtr
        {
            get { return mSplashAdPtr; }

            set
            {
                Externs.TXADRelease(mSplashAdPtr);
                mSplashAdPtr = value;
            }
        }

        public SplashClient(string adUnitId)
        {
        }
        
        public event EventHandler<AdEventArgs> OnAdLoaded;
        public event EventHandler<AdEventArgs> OnAdShown;
        public event EventHandler<AdEventArgs> OnAdClicked;
        public event EventHandler<AdEventArgs> OnAdSkipped;
        public event EventHandler<AdEventArgs> OnAdClosed;
        public event EventHandler<AdFailedToLoadEventArgs> OnAdFailedToLoad;

        #region ISplashClient

        public void SetExpressAdSize(float width, float height)
        {
        }

        public void SetMuted(bool muted)
        {
        }

        public void SetBottomView(string bottomView)
        {
        }

        public void SetBottomText(string title, string desc)
        {
        }

        public void SetNetworkConfigs(NetworkConfigs networkConfigs)
        {
        }

        public void SetLineItemFilter(LineItemFilter filter)
        {
        }

        public void LoadAd()
        {
        }

        public bool IsReady() 
        {
            return false;
        }

        public void Show(SplashOrientation orientation)
        {
        }

        public void Show(string sceneId, SplashOrientation orientation)
        {
        }
        #endregion


        #region IDisposable
        public void Destroy()
        {
            SplashAdPtr = IntPtr.Zero;
        }

        public void Dispose()
        {
            Destroy();
            ((GCHandle)mSplashClientPtr).Free();
        }

        ~SplashClient()
        {
            Dispose();
        }

        #endregion
    }
}
