using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using GameWish.Game;
using System.ComponentModel;

namespace Qarth
{
    public enum ShowAnimType
    {
        Top,
        Bottom,
        Left,
        Right,
        Scale,
        Fade,
        ScaleY,
        ScaleX,
    }
    public class ElementUIAnimCustomStart : MonoBehaviour
    {
        public ShowAnimType m_ShowAnimType;
        public float m_BlingIndexTime = 0;
        public Transform m_BlingParent;
        public float delay = 0;
        [HideInInspector]
        public int customSibling = -1;//设置这个就不自动获取
        [HideInInspector] 
        public bool closeAnim;//开关动画

        public bool hideAtStart;
        
        private CanvasGroup m_CanvasGroup;
        private RectTransform m_RectTransform;
        private Vector2 m_StartPos;
        private Vector3 m_StartScale;
        private string m_TweenId;
        public RectTransform rectTransform
        {
            get
            {
                if (m_RectTransform == null)
                {
                    m_RectTransform = GetComponent<RectTransform>();
                }
                return m_RectTransform;
            }
        }
        public string tweenId
        {
            get
            {
                if (string.IsNullOrEmpty(m_TweenId))
                {
                    m_TweenId = GetTweenId();
                }
                return m_TweenId;
            }
        }
        /// <summary>
        /// Awake is called when the script instance is being loaded.
        /// </summary>
        void Awake()
        {
            m_StartPos = rectTransform.anchoredPosition;
            m_StartScale = rectTransform.localScale;
            m_CanvasGroup = GetComponent<CanvasGroup>();
        }

        void Start()
        {
            if (hideAtStart)
                HideAnim(0);
        }

        public void ShowAnim(float duration = 0.2f, Action onComplete = null)
        {
            if (closeAnim)
            {
                return;
            }
            ResetState();
            int m_BlingIndex;
            if (customSibling >= 0)
            {
                m_BlingIndex = customSibling;
            }
            else
            {
                m_BlingIndex = m_BlingParent == null ? transform.GetSiblingIndex() : m_BlingParent.GetSiblingIndex();
            }

            switch (m_ShowAnimType)
            {
                case ShowAnimType.Top:
                case ShowAnimType.Bottom:
                case ShowAnimType.Left:
                case ShowAnimType.Right:
                    rectTransform.DOAnchorPos(m_StartPos, duration).SetEase(Ease.OutCubic)
                        .SetDelay(m_BlingIndexTime * m_BlingIndex+delay)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });
                    break;
                case ShowAnimType.Scale:
                case ShowAnimType.ScaleY:
                case ShowAnimType.ScaleX:
                    rectTransform.DOScale(m_StartScale, duration).SetEase(Ease.OutBack)
                        .SetDelay(m_BlingIndexTime * m_BlingIndex+delay)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });;
                    break;
                case ShowAnimType.Fade:
                    m_CanvasGroup.DOFade(1, duration).SetEase(Ease.OutCubic)
                        .SetDelay(m_BlingIndexTime * m_BlingIndex+delay)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });;
                    break;
            }
        }
       public void HideAnim(float duration = 0.2f, Action onComplete = null)
        {
            DOTween.Kill(tweenId);

            var targetPos=Vector2.zero;
            var targetScale = Vector3.zero;
            switch (m_ShowAnimType)
            {
                case ShowAnimType.Top:
                    float yOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.height / 2 + rectTransform.rect.height * rectTransform.pivot.y;
                    targetPos = new Vector2(rectTransform.anchoredPosition.x, yOffset + 50);
                    break;
                case ShowAnimType.Bottom:
                    yOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.height / 2 + rectTransform.rect.height * rectTransform.pivot.y;
                    targetPos = new Vector2(rectTransform.anchoredPosition.x, -yOffset - 50);
                    break;
                case ShowAnimType.Left:
                    float xOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.width / 2 + rectTransform.rect.width * rectTransform.pivot.x;
                    targetPos = new Vector2(-xOffset - 50, rectTransform.anchoredPosition.y);
                    break;
                case ShowAnimType.Right:
                    xOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.width / 2 + rectTransform.rect.width * rectTransform.pivot.x;
                    targetPos = new Vector2(xOffset + 50, rectTransform.anchoredPosition.y);
                    break;
                case ShowAnimType.Scale:
                    targetScale = Vector3.zero;
                    break;
                case ShowAnimType.Fade:
                    if (m_CanvasGroup == null)
                    {
                        m_CanvasGroup = gameObject.AddMissingComponent<CanvasGroup>();
                    }
                    m_CanvasGroup.DOKill();
                    m_CanvasGroup.alpha = 1;
                    break;
                case ShowAnimType.ScaleY:
                    targetScale = Vector3.right+Vector3.forward;
                    break;
                case ShowAnimType.ScaleX:
                    targetScale = Vector3.up+Vector3.forward;
                    break;
            }
            
            switch (m_ShowAnimType)
            {
                case ShowAnimType.Top:
                case ShowAnimType.Bottom:
                case ShowAnimType.Left:
                case ShowAnimType.Right:
                    rectTransform.DOAnchorPos(targetPos, duration).SetEase(Ease.OutCubic)
                        .SetDelay(m_BlingIndexTime)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });
                    break;
                case ShowAnimType.Scale:
                case ShowAnimType.ScaleY:
                case ShowAnimType.ScaleX:
                    rectTransform.DOScale(targetScale, duration).SetEase(Ease.OutBack)
                        .SetDelay(m_BlingIndexTime)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });
                    break;
                case ShowAnimType.Fade:
                    m_CanvasGroup.DOFade(0, duration).SetEase(Ease.OutCubic)
                        .SetDelay(m_BlingIndexTime)
                        .SetId(tweenId)
                        .OnComplete(() =>
                        {
                            onComplete?.Invoke();
                        });
                    break;
            }

        }

         void ResetState()
        {
            DOTween.Kill(tweenId);
            switch (m_ShowAnimType)
            {
                case ShowAnimType.Top:
                    float yOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.height / 2 + rectTransform.rect.height * rectTransform.pivot.y;
                    rectTransform.anchoredPosition = new Vector2(rectTransform.anchoredPosition.x, yOffset + 50);
                    break;
                case ShowAnimType.Bottom:
                    yOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.height / 2 + rectTransform.rect.height * rectTransform.pivot.y;
                    rectTransform.anchoredPosition = new Vector2(rectTransform.anchoredPosition.x, -yOffset - 50);
                    break;
                case ShowAnimType.Left:
                    float xOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.width / 2 + rectTransform.rect.width * rectTransform.pivot.x;
                    rectTransform.anchoredPosition = new Vector2(-xOffset - 50, rectTransform.anchoredPosition.y);
                    break;
                case ShowAnimType.Right:
                    xOffset = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.width / 2 + rectTransform.rect.width * rectTransform.pivot.x;
                    rectTransform.anchoredPosition = new Vector2(xOffset + 50, rectTransform.anchoredPosition.y);
                    break;
                case ShowAnimType.Scale:
                    rectTransform.localScale = Vector3.zero;
                    break;
                case ShowAnimType.Fade:
                    if (m_CanvasGroup == null)
                    {
                        m_CanvasGroup = gameObject.AddMissingComponent<CanvasGroup>();
                    }
                    m_CanvasGroup.DOKill();
                    m_CanvasGroup.alpha = 0;
                    break;
                case ShowAnimType.ScaleY:
                    rectTransform.localScale = Vector3.right+Vector3.forward;
                    break;
                case ShowAnimType.ScaleX:
                    rectTransform.localScale = Vector3.up+Vector3.forward;
                    break;
            }
        
        }

        string GetTweenId()
        {
            return GetInstanceID() + m_ShowAnimType.ToString();
        }
    }
}
