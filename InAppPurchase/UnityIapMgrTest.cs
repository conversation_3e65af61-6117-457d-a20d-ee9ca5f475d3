using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    /// <summary>
    /// Unity IAP 5.0 升级测试脚本
    /// 用于验证UnityIapMgr的升级是否正确
    /// </summary>
    public class UnityIapMgrTest : MonoBehaviour
    {
        [Header("测试配置")]
        public string testProductId = "com.test.product";
        
        void Start()
        {
            // 测试IAP初始化
            TestIapInitialization();
        }
        
        void TestIapInitialization()
        {
            Debug.Log("[UnityIapMgrTest] 开始测试IAP初始化...");
            
            // 检查单例是否正常工作
            var iapMgr = UnityIapMgr.S;
            if (iapMgr != null)
            {
                Debug.Log("[UnityIapMgrTest] ✓ UnityIapMgr单例创建成功");
                
                // 初始化IAP
                iapMgr.Init();
                
                // 检查初始化状态
                StartCoroutine(CheckInitializationStatus());
            }
            else
            {
                Debug.LogError("[UnityIapMgrTest] ✗ UnityIapMgr单例创建失败");
            }
        }
        
        System.Collections.IEnumerator CheckInitializationStatus()
        {
            float timeout = 10f;
            float elapsed = 0f;
            
            while (elapsed < timeout)
            {
                if (UnityIapMgr.S.IsIapInit)
                {
                    Debug.Log("[UnityIapMgrTest] ✓ IAP初始化成功");
                    TestProductRetrieval();
                    yield break;
                }
                
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            Debug.LogWarning("[UnityIapMgrTest] ⚠ IAP初始化超时");
        }
        
        void TestProductRetrieval()
        {
            Debug.Log("[UnityIapMgrTest] 测试产品获取...");
            
            var product = UnityIapMgr.S.GetProductById(testProductId);
            if (product != null)
            {
                Debug.Log($"[UnityIapMgrTest] ✓ 成功获取产品: {product.definition.id}");
                Debug.Log($"[UnityIapMgrTest] 产品价格: {product.metadata.localizedPriceString}");
            }
            else
            {
                Debug.Log($"[UnityIapMgrTest] ⚠ 未找到产品: {testProductId}");
            }
        }
        
        [ContextMenu("测试购买")]
        public void TestPurchase()
        {
            if (!UnityIapMgr.S.IsIapInit)
            {
                Debug.LogWarning("[UnityIapMgrTest] IAP未初始化，无法测试购买");
                return;
            }
            
            Debug.Log($"[UnityIapMgrTest] 开始测试购买产品: {testProductId}");
            UnityIapMgr.S.DoPurchase(testProductId);
        }
        
        [ContextMenu("测试恢复购买")]
        public void TestRestore()
        {
            if (!UnityIapMgr.S.IsIapInit)
            {
                Debug.LogWarning("[UnityIapMgrTest] IAP未初始化，无法测试恢复购买");
                return;
            }
            
            Debug.Log("[UnityIapMgrTest] 开始测试恢复购买");
            UnityIapMgr.S.Restore();
        }
        
        void OnEnable()
        {
            // 监听IAP事件
            EventSystem.S.Register(SDKEventID.OnPurchaseInitSuccess, OnPurchaseInitSuccess);
            EventSystem.S.Register(SDKEventID.OnPurchaseSuccess, OnPurchaseSuccess);
            EventSystem.S.Register(SDKEventID.OnPurchaseRestore, OnPurchaseRestore);
        }
        
        void OnDisable()
        {
            // 取消监听IAP事件
            EventSystem.S.UnRegister(SDKEventID.OnPurchaseInitSuccess, OnPurchaseInitSuccess);
            EventSystem.S.UnRegister(SDKEventID.OnPurchaseSuccess, OnPurchaseSuccess);
            EventSystem.S.UnRegister(SDKEventID.OnPurchaseRestore, OnPurchaseRestore);
        }
        
        void OnPurchaseInitSuccess(int key, params object[] args)
        {
            Debug.Log("[UnityIapMgrTest] ✓ 收到购买初始化成功事件");
        }
        
        void OnPurchaseSuccess(int key, params object[] args)
        {
            Debug.Log("[UnityIapMgrTest] ✓ 收到购买成功事件");
            if (args.Length > 0)
            {
                var product = args[0] as UnityEngine.Purchasing.Product;
                if (product != null)
                {
                    Debug.Log($"[UnityIapMgrTest] 购买成功的产品: {product.definition.id}");
                }
            }
        }
        
        void OnPurchaseRestore(int key, params object[] args)
        {
            Debug.Log("[UnityIapMgrTest] ✓ 收到购买恢复事件");
        }
    }
}
