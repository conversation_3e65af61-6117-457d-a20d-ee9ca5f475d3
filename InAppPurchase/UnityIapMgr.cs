using System.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Security;
using UnityEngine.Purchasing.Extension;
using LitJson;

namespace Qarth
{
    public class UnityIapMgr : TMonoSingleton<UnityIapMgr>
    {
        private StoreController m_Controller;
        private bool m_IsPurchaseInProgress = false;
        private string m_CurrentPurchasingID = "";
        private bool m_IsWaitPauseEvent;

        private float m_PurchaseRequestTime;
        private IAppleStoreExtendedPurchaseService m_AppleExtensions;
        private IGooglePlayStoreExtendedPurchaseService m_GooglePlayStoreExtensions;

        public bool IsIapInit
        {
            get { return m_Controller != null; }
        }

        private Dictionary<string, ProductCatalogItem> m_DicCatalogItems = new Dictionary<string, ProductCatalogItem>();

        private Dictionary<string, object> m_DicCustomPurchaseSuccEvtParams = new Dictionary<string, object>();

        private CrossPlatformValidator m_IapValidator;
        private int m_ReInitCount = 0;
        private int m_ReInitTimer = -1;

        public override void OnSingletonInit()
        {
            // Log.i("[UnityIapMgr.OnSingletonInit] IAP Init Success");
        }

        public void InitPurchseInfo()
        {
        }

        public void Init()
        {
            InitIap();
        }

        async void InitIap()
        {
            ThreadMgr.S.Init();

            if (m_Controller != null)
            {
                return;
            }

            // Initialize the new IAP 5.0 system
            m_Controller = UnityIAPServices.StoreController();

            // Set up event handlers
            m_Controller.OnPurchasePending += OnPurchasePending;
            m_Controller.OnPurchaseConfirmed += OnPurchaseConfirmed;
            m_Controller.OnPurchaseFailed += OnPurchaseFailed;
            m_Controller.OnCheckEntitlement += OnCheckEntitlement;
            m_Controller.OnStoreDisconnected += OnStoreDisconnected;
            
            // Initialize validator
            InitializeValidator();

            Timer.S.Post2Scale((c) =>
            {
                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("state", "start")
                    .CustomEventDic(DataAnalysisDefine.PRUCHASE_INIT_STATE, DAPE.ThinkingData);
            }, 5);

            try
            {
                await m_Controller.Connect();
                m_Controller.OnProductsFetched += OnProductsFetched;
                m_Controller.OnProductsFetchFailed += OnProductsFetchFailed;
                FetchProducts();
            }
            catch (System.Exception ex)
            {
                OnInitializeFailed(ex.Message);
            }
        }

        void InitializeValidator()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                // IAP 5.0 直接使用 GooglePlayTangle.Data() 静态方法
                var googlePlayKey = GooglePlayTangle.Data();
                m_IapValidator = new CrossPlatformValidator(googlePlayKey, null, Application.identifier);
                Log.i("[UnityIapMgr] Google Play validator initialized successfully");
            }
            catch (System.Exception ex)
            {
                Log.w("[UnityIapMgr] Failed to initialize Google Play validator: " + ex.Message);
                m_IapValidator = null;
            }
#elif UNITY_IOS && !UNITY_EDITOR
            try
            {
                // 检查AppleTangle类是否存在
                var appleTangleType = System.Type.GetType("UnityEngine.Purchasing.Security.AppleTangle");
                if (appleTangleType != null)
                {
                    // IAP 5.0 直接使用 AppleTangle.Data() 静态方法
                    var dataMethod = appleTangleType.GetMethod("Data", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                    if (dataMethod != null)
                    {
                        var appleRootCert = (byte[])dataMethod.Invoke(null, null);
                        m_IapValidator = new CrossPlatformValidator(null, appleRootCert, Application.identifier);
                        Log.i("[UnityIapMgr] Apple validator initialized successfully");
                    }
                    else
                    {
                        Log.w("[UnityIapMgr] AppleTangle.Data() method not found");
                        m_IapValidator = null;
                    }
                }
                else
                {
                    Log.w("[UnityIapMgr] AppleTangle class not found, validator not initialized");
                    m_IapValidator = null;
                }
            }
            catch (System.Exception ex)
            {
                Log.w("[UnityIapMgr] Failed to initialize Apple validator: " + ex.Message);
                m_IapValidator = null;
            }
#else
            // 在编辑器中不初始化验证器
            m_IapValidator = null;
            Log.i("[UnityIapMgr] Validator not initialized in editor mode");
#endif
        }

        void FetchProducts()
        {
            ProductCatalog catalog = ProductCatalog.LoadDefaultCatalog();
            m_DicCatalogItems.Clear();

            var productsToFetch = new List<ProductDefinition>();

            foreach (var product in catalog.allProducts)
            {
                m_DicCatalogItems.Add(product.id, product);

                if (product.allStoreIDs.Count > 0)
                {
                    // For IAP 5.0, we need to use the primary store ID as the storeSpecificId
                    foreach (var storeID in product.allStoreIDs)
                    {
                        if (!string.IsNullOrEmpty(storeID.id))
                            productsToFetch.Add(new ProductDefinition(product.id, storeID.id, product.type));
                    }
                }
                else
                {
                    productsToFetch.Add(new ProductDefinition(product.id, product.type));
                }
            }

            m_Controller.FetchProducts(productsToFetch);
        }

        // New IAP 5.0 event handlers
        void OnProductsFetched(List<Product> products)
        {
            Log.i("[UnityIapMgr.Iap] IAP Init Success");
            for (int i = 0; i < products.Count; i++)
            {
                Log.i("[UnityIapMgr.Iap] IAP Product:" + products[i].definition.id);
            }

#if UNITY_ANDROID
            m_GooglePlayStoreExtensions = m_Controller.GooglePlayStoreExtendedPurchaseService;
#endif
#if UNITY_IOS
            m_AppleExtensions = m_Controller.AppleStoreExtendedPurchaseService;
#endif

            EventSystem.S.Send(SDKEventID.OnPurchaseInitSuccess);

            Timer.S.Post2Scale((c) =>
            {
                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("state", "sucess")
                    .AddEventParam("iapitem_num", products.Count)
                    .CustomEventDic(DataAnalysisDefine.PRUCHASE_INIT_STATE, DAPE.ThinkingData);
                if (m_Controller != null)
                {
                    QueryMissOrder();
                }
            }, 5);
        }

        void OnProductsFetchFailed(ProductFetchFailed failure)
        {
            OnInitializeFailed( failure.FailureReason);
        }

        void OnStoreDisconnected(StoreConnectionFailureDescription description)
        {
            Log.e("[UnityIapMgr.Iap] Store disconnected: " + description.message);
        }

        public void OnInitializeFailed(InitializationFailureReason error)
        {
            Log.i("[UnityIapMgr.Iap] IAP Init Error");

            Timer.S.Post2Scale((c) =>
            {
                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("state", "fail")
                    .AddEventParam("reason", error.ToString())
                    .CustomEventDic(DataAnalysisDefine.PRUCHASE_INIT_STATE, DAPE.ThinkingData);
            }, 5);
        }
        public void OnInitializeFailed(string message)
        {
            Timer.S.Post2Scale((c) =>
                        {
                            DataAnalysisMgr.S.ResetEventMap()
                                .AddEventParam("state", "fail")
                                .AddEventParam("reason", message)
                                .CustomEventDic(DataAnalysisDefine.PRUCHASE_INIT_STATE, DAPE.ThinkingData);
                        }, 5);
        }


        private void OnApplicationPause(bool pauseStatus)
        {
            m_IsWaitPauseEvent = false;
        }

        public void DoPurchase(string productId)
        {
            if (m_IsPurchaseInProgress)
            {
                Log.w("In Purchase progressing");
                return;
            }

            if (m_Controller == null)
            {
                Log.e("Purchase Not Init ");
                return;
            }

            if (SecurityMgr.S.isBeHacked)
            {
                Log.i("security hacked");
                return;
            }

            if (m_Controller.GetProductById(productId) == null)
            {
                Log.e("No Product has ID" + productId);
                return;
            }

            m_IsPurchaseInProgress = true;
            m_CurrentPurchasingID = productId;
            RecordIapInfo(productId);

            m_PurchaseRequestTime = Time.realtimeSinceStartup;
            DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_REQUEST, DAPE.ThinkingData, productId);
            m_Controller.PurchaseProduct(productId);
        }


        /// <summary>
        /// IAP 5.0 购买待处理事件
        /// </summary>
        /// <param name="order"></param>
        void OnPurchasePending(PendingOrder order)
        {
            var product = GetFirstProductInOrder(order);
            if (product == null)
            {
                Log.e("Could not find product in order.");
                return;
            }

            if (string.IsNullOrEmpty(m_CurrentPurchasingID))
            {
                //安卓会自动调用restore
                EventSystem.S.Send(SDKEventID.OnPurchaseRestore, product);
                m_Controller.ConfirmPurchase(order);
                return;
            }

            //校验
            bool validPurchase = true;
            float passTime = Time.realtimeSinceStartup - m_PurchaseRequestTime;

#if UNITY_EDITOR
            EventSystem.S.Send(SDKEventID.OnPurchaseSuccess, product);
#endif

            if (m_IapValidator != null)
            {
                try
                {
                    var result = m_IapValidator.Validate(order.Info.Receipt);
                    foreach (var receipt in result)
                    {
                        Log.e($"Product ID: {receipt.productID}\n" +
                                  $"Purchase Date: {receipt.purchaseDate}\n" +
                                  $"Transaction ID: {receipt.transactionID}");
                    }
                }
                catch (IAPSecurityException)
                {
                    validPurchase = false;
                }
            }

            if (passTime > 3 && validPurchase)
            {
                ProductCatalogItem item = null;
                m_DicCatalogItems.TryGetValue(m_CurrentPurchasingID, out item);
                double value = 0;
                if (item != null)
                {
                    Log.e(">>>>>>>>>>>>>>>>" +1232132112);
                    if (!string.IsNullOrEmpty(item.defaultDescription.Description))
                    {
                        value = item.defaultDescription.Description.Parse2Double();
                    }

                    if (item.type == ProductType.Subscription)
                    {
                        DataAnalysisMgr.S.ResetEventMap()
                            .AddEventParam("pass_time", passTime)
                            .AddEventParam("product_id", m_CurrentPurchasingID)
                            .AddEventParam("purchase_value", value)
                            .CustomEventDic(DataAnalysisDefine.SUBSCRIBE_SUCCESS, DAPE.ALL);
                    }
                    else
                    {
                        AdValueEvtMgr.S.RecordTaichiAdRevenue(value);

                        DataAnalysisMgr.S.ResetEventMap();
                        if (m_DicCustomPurchaseSuccEvtParams.Count > 0)
                        {
                            foreach (var key in m_DicCustomPurchaseSuccEvtParams.Keys)
                            {
                                DataAnalysisMgr.S.AddEventParam(key, m_DicCustomPurchaseSuccEvtParams[key]);
                            }
                        }

                        DataAnalysisMgr.S.AddEventParam("pass_time", passTime)
                            .AddEventParam("product_id", m_CurrentPurchasingID)
                            .AddEventParam("purchase_value", value)
                            .AddEventParam("current_iso", product.metadata.isoCurrencyCode)
                            .AddEventParam("localizedPrice", product.metadata.localizedPrice)
                            .CustomEventDic(DataAnalysisDefine.PURCHASE_SUCCESS, DAPE.ALL);

                        EventSystem.S.Send(SDKEventID.OnIAPRevenueStep, value);
                    }
                    DataAnalysisMgr.S.SetUserProp(DataAnalysisDefine.PROP_LAST_IAP, DAPE.ThinkingData, m_CurrentPurchasingID);
                }
                Log.e(">>>>>>>>>>>>>>>>" +4564564564564);
                EventSystem.S.Send(SDKEventID.OnPurchaseSuccess, product);
                PlayerPrefs.SetInt("PurchaseUserTag", 1);
            }
            else
            {
                Log.e(">>>>>>>>>>>>>>>>" +8699878978);
                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("pass_time", passTime)
                    .CustomEventDic(DataAnalysisDefine.PURCHASE_FAKE, DAPE.ThinkingData);
            }

            if (!string.IsNullOrEmpty(m_IapRecordLastTimeStamp))
            {
                RemoveKey(m_IapRecordLastTimeStamp);
                m_IapRecordLastTimeStamp = null;
            }

            m_IsPurchaseInProgress = false;
            m_CurrentPurchasingID = null;

            // Confirm the purchase
            m_Controller.ConfirmPurchase(order);
        }

        /// <summary>
        /// IAP 5.0 购买确认事件
        /// </summary>
        /// <param name="order"></param>
        void OnPurchaseConfirmed(Order order)
        {
            var product = GetFirstProductInOrder(order);
            switch (order)
            {
                case ConfirmedOrder confirmedOrder:
                    Log.i($"Purchase confirmed - Product: {product?.definition.id}");
                    break;
                case FailedOrder failedOrder:
                    Log.e($"Purchase confirmation failed - Product: {product?.definition.id}, Reason: {failedOrder.FailureReason}");
                    break;
            }
        }

        Product GetFirstProductInOrder(Order order)
        {
            return order.CartOrdered.Items().FirstOrDefault()?.Product;
        }

        /// <summary>
        /// IAP 5.0 购买失败事件
        /// </summary>
        /// <param name="order"></param>
        void OnPurchaseFailed(FailedOrder order)
        {
            var product = GetFirstProductInOrder(order);

            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("failure_reason", order.FailureReason)
                .AddEventParam("product_id", product?.definition.id ?? "unknown")
                .CustomEventDic(DataAnalysisDefine.PURCHASE_FAILED, DAPE.ThinkingData);

            m_IsPurchaseInProgress = false;
            m_CurrentPurchasingID = null;

            if (!string.IsNullOrEmpty(m_IapRecordLastTimeStamp))
            {
                RemoveKey(m_IapRecordLastTimeStamp);
                m_IapRecordLastTimeStamp = null;
            }
        }

        /// <summary>
        /// IAP 5.0 权益检查事件
        /// </summary>
        /// <param name="entitlement"></param>
        void OnCheckEntitlement(Entitlement entitlement)
        {
            if (entitlement == null || entitlement.Product == null)
                return;
            // 处理订阅权益检查
            Log.i($"Entitlement checked - Product: {entitlement.Product.definition.id}, Status: {entitlement.Status}");
        }

        public Product GetProductById(string id)
        {
            if (IsIapInit)
            {
                return m_Controller.GetProducts().FirstOrDefault(p => p.definition.id == id);
            }

            return null;
        }

        public bool IsItemReceipt(string productId)
        {
            if (IsIapInit)
            {
                var product = m_Controller.GetProducts().FirstOrDefault(p => p.definition.id == productId);
                return product != null && product.hasReceipt;
            }

            return false;
        }

        public bool IsSubscribedTo(string productId)
        {
#if UNITY_EDITOR
            return PlayerPrefs.GetInt("subscribe", 0) == 1;
#endif
            var product = m_Controller.GetProducts().FirstOrDefault(p => p.definition.id == productId);
            if (product == null)
            {
                return false;
            }

            // Use the new IAP 5.0 entitlement checking
            m_Controller.CheckEntitlement(product);

            // For backward compatibility, also check using SubscriptionManager
            if (product.receipt == null)
            {
                return false;
            }

            var subscriptionManager = new SubscriptionManager(product, null);
            var info = subscriptionManager.getSubscriptionInfo();
            return info.isSubscribed() == Result.True;
        }

        public void Restore()
        {
            if (!IsIapInit)
            {
                return;
            }

            if (PlayerPrefs.GetInt("IsRestore", 0) > 0)
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("IsRestore"));
                return;
            }

            m_CurrentPurchasingID = null;
            m_Controller.RestoreTransactions(OnRestore);
        }

        void OnRestore(bool success, string error)
        {
            if (success)
            {
                PlayerPrefs.SetInt("IsRestore", 1);
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("Restore_Suc"));
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("Restore_Fail"));
                Log.e("Restore failed: " + error);
            }
        }


        #region customevt

        public void AddCustomParam2PurchaseSuccEvt(string key, object value)
        {
            if (!m_DicCustomPurchaseSuccEvtParams.ContainsKey(key))
                m_DicCustomPurchaseSuccEvtParams.Add(key, value);
        }

        #endregion
        #region 处理丢单
        private string m_IapRecordKey = "iap_record_key";
        private string m_IapMissOrderKey = "iap_missorder_record_key";
        private string m_IapRecordLastTimeStamp;
        void RecordIapInfo(string product)
        {
            m_IapRecordLastTimeStamp = CustomExtensions.GetTimeStamp();
            string record = PlayerPrefs.GetString(m_IapRecordKey, "{}");
            Dictionary<string, string> dicRecord = JsonMapper.ToObject<Dictionary<string, string>>(record);
            dicRecord[m_IapRecordLastTimeStamp] = product;
            PlayerPrefs.SetString(m_IapRecordKey, JsonMapper.ToJson(dicRecord));
            PlayerPrefs.Save();
        }
        void RemoveKey(string timestamp)
        {
            string record = PlayerPrefs.GetString(m_IapRecordKey, "{}");
            Dictionary<string, string> dicRecord = JsonMapper.ToObject<Dictionary<string, string>>(record);
            if (dicRecord.ContainsKey(timestamp))
            {
                dicRecord.Remove(timestamp);
            }
            PlayerPrefs.SetString(m_IapRecordKey, JsonMapper.ToJson(dicRecord));
            PlayerPrefs.Save();
        }
        void QueryMissOrder()
        {
            if (m_IapValidator == null)
            {
                return;
            }

            var products = m_Controller.GetProducts();
            for (int i = 0; i < products.Count; i++)
            {
                if (products[i].receipt != null)
                {
                    try
                    {
                        var result = m_IapValidator.Validate(products[i].receipt);
                        foreach (var receipt in result)
                        {
                            DoMissOrder(receipt.productID, receipt.purchaseDate);
                        }
                    }
                    catch (IAPSecurityException)
                    {
                    }
                }
            }
        }
        void DoMissOrder(string productId, DateTime purchaseTime)
        {
            string record = PlayerPrefs.GetString(m_IapRecordKey, "{}");
            Dictionary<string, string> dicRecord = JsonMapper.ToObject<Dictionary<string, string>>(record);
            foreach (var item in dicRecord.Keys)
            {
                DateTime timeRecord = CustomExtensions.GetTimeFromTimestamp(item);
                string productRecord = dicRecord[item];
                if (productId.Equals(productRecord))
                {
                    var deltTime = purchaseTime.Subtract(timeRecord).TotalSeconds;
                    // if (deltTime < 600 && deltTime > -600)
                    // {
                    //     GetMissOrderPurchaseReward(productRecord);
                    //     RemoveKey(item);
                    // }
                    GetMissOrderPurchaseReward(productRecord);
                    RemoveKey(item);
                }
            }
        }
        void GetMissOrderPurchaseReward(string productId)
        {
            var productItem = m_Controller.GetProducts().FirstOrDefault(p => p.definition.id == productId);
            if (productItem == null)
            {
                return;
            }
            ProductCatalogItem item = null;
            m_DicCatalogItems.TryGetValue(productId, out item);
            double value = 0;
            if (item != null)
            {
                if (!string.IsNullOrEmpty(item.defaultDescription.Description))
                {
                    value = item.defaultDescription.Description.Parse2Double();
                }

                AdValueEvtMgr.S.RecordTaichiAdRevenue(value);

                DataAnalysisMgr.S.ResetEventMap();

                DataAnalysisMgr.S.AddEventParam("pass_time", 10)
                    .AddEventParam("product_id", productId)
                    .AddEventParam("purchase_value", value)
                    .AddEventParam("value", value)
                    .AddEventParam("country", CountryHelper.CountryLv)
                    .AddEventParam("current_iso", productItem.metadata.isoCurrencyCode)
                    .AddEventParam("localizedPrice", productItem.metadata.localizedPrice)
                    .CustomEventDic(DataAnalysisDefine.PURCHASE_SUCCESS, DAPE.ALL);

                EventSystem.S.Send(SDKEventID.OnIAPRevenueStep, value);
            }

            EventSystem.S.Send(SDKEventID.OnPurchaseSuccess, productItem, true);
            PlayerPrefs.SetInt("PurchaseUserTag", 1);
            RecordMissOrderId(productId);
        }
        void RecordMissOrderId(string productId)
        {
            string globalProductId = productId;
            string record = PlayerPrefs.GetString(m_IapMissOrderKey, "{}");
            List<string> lstRecord = JsonMapper.ToObject<List<string>>(record);
            if (!lstRecord.Contains(globalProductId))
            {
                lstRecord.Add(globalProductId);
            }
            PlayerPrefs.SetString(m_IapMissOrderKey, JsonMapper.ToJson(lstRecord));
            PlayerPrefs.Save();
        }
        public void CheckMissOrderId(string globalProductId)
        {
            string record = PlayerPrefs.GetString(m_IapMissOrderKey, "{}");
            List<string> lstRecord = JsonMapper.ToObject<List<string>>(record);
            if (lstRecord.Contains(globalProductId))
            {
                lstRecord.Remove(globalProductId);
                var p = m_Controller.GetProducts().FirstOrDefault(product => product.definition.id == globalProductId);
                if (p != null)
                {
                    EventSystem.S.Send(SDKEventID.OnPurchaseSuccess, p);
                }
            }
            PlayerPrefs.SetString(m_IapMissOrderKey, JsonMapper.ToJson(lstRecord));
            PlayerPrefs.Save();
        }
        #endregion
    }
}