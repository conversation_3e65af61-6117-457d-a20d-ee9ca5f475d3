using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using LitJson;
using UnityEngine.Purchasing;
using Proyecto26;
using AppsFlyerSDK;

namespace Qarth
{
    /*
    public class PurchaseInitResult
    {
        public class PurchaseItemInfo
        {
            public string priceLocale;
            public string productIdentifier;
        }

        private List<PurchaseItemInfo> m_PurchaseDataList;

        public PurchaseInitResult(string msg)
        {
            m_PurchaseDataList = JsonMapper.ToObject<List<PurchaseItemInfo>>(msg);
        }
    }
    */

    [TMonoSingletonAttribute("PurchaseMgr")]
    public class PurchaseMgr : TMonoSingleton<PurchaseMgr>, IStoreListener
    {
        [SerializeField]
        private string m_GPPublicAPIKey;

        private IStoreController m_Controller;
        private bool m_IsPurchaseInProgress = false;
        private string m_CurrentPurchasingID = "";
        private bool m_IsWaitPauseEvent;


        private IAppleExtensions m_AppleExtensions;
        private IGooglePlayStoreExtensions m_GooglePlayStoreExtensions;
        private bool m_IsGooglePlayStoreSelected;

        private Dictionary<string, SubscriptionInfo> SubscriptionManagerDic = new Dictionary<string, SubscriptionInfo>();
        private int m_PurchaseCancelTimer = -1;
        private int m_PurchaseValidTimer = -1;
        private Product m_PurchasedProduct;


        private List<string> m_PurchasedKeysList = new List<string>();

        public override void OnSingletonInit()
        {
            Log.i("[UnityIapMgr.OnSingletonInit] IAP Init Success");
        }

        public void InitPurchaseInfo()
        {

        }

        public void OnInitializeFailed(InitializationFailureReason reason, string msg)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="key">对应的是 ioskey 和  android key</param>
        public bool CheckItemHadCharged(string key)
        {
            return m_PurchasedKeysList.Contains(key);
        }

        public void Init()
        {
            var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());
            m_IsGooglePlayStoreSelected = StandardPurchasingModule.Instance().appStore == AppStore.GooglePlay && Application.platform == RuntimePlatform.Android;
            var datalist = TDPurchaseTable.dataList;
            ThreadMgr.S.Init();

            if (datalist.Count == 0 || datalist == null)
            {
                return;
            }

            if (m_Controller != null)
            {
                return;
            }

            for (int i = 0; i < datalist.Count; i++)
            {
                // builder.AddProduct(datalist[i].id, (ProductType)datalist[i].productType, new IDs()
                // {
                //     {datalist[i].androidKey, GooglePlay.Name},
                //     {datalist[i].iOSKey, AppleAppStore.Name}
                // });
            }
            UnityPurchasing.Initialize(this, builder);
            EventSystem.S.Register(SDKEventID.OnPurchaseValidated, OnValidationCompletion);
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            m_IsWaitPauseEvent = false;
        }

        private bool checkIfProductIsAvailableForSubscriptionManager(string receipt)
        {
            var receipt_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode(receipt);
            if (!receipt_wrapper.ContainsKey("Store") || !receipt_wrapper.ContainsKey("Payload"))
            {
                Debug.Log("The product receipt does not contain enough information");
                return false;
            }
            var store = (string)receipt_wrapper["Store"];
            var payload = (string)receipt_wrapper["Payload"];

            if (payload != null)
            {
                switch (store)
                {
                    case GooglePlay.Name:
                        {
                            var payload_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode(payload);
                            if (!payload_wrapper.ContainsKey("json"))
                            {
                                Debug.Log("The product receipt does not contain enough information, the 'json' field is missing");
                                return false;
                            }
                            var original_json_payload_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode((string)payload_wrapper["json"]);
                            if (original_json_payload_wrapper == null || !original_json_payload_wrapper.ContainsKey("developerPayload"))
                            {
                                Debug.Log("The product receipt does not contain enough information, the 'developerPayload' field is missing");
                                return false;
                            }
                            var developerPayloadJSON = (string)original_json_payload_wrapper["developerPayload"];
                            var developerPayload_wrapper = (Dictionary<string, object>)MiniJson.JsonDecode(developerPayloadJSON);
                            if (developerPayload_wrapper == null || !developerPayload_wrapper.ContainsKey("is_free_trial") || !developerPayload_wrapper.ContainsKey("has_introductory_price_trial"))
                            {
                                Debug.Log("The product receipt does not contain enough information, the product is not purchased using 1.19 or later");
                                return false;
                            }
                            return true;
                        }
                    case AppleAppStore.Name:
                    case MacAppStore.Name:
                        {
                            return true;
                        }
                    default:
                        {
                            return false;
                        }
                }
            }
            return false;
        }

        public void DoPurchase(TDPurchase data)
        {
            DoPurchase(data.id);
        }

        public void DoPurchase(string id)
        {
            if (m_IsPurchaseInProgress)
            {
                EventSystem.S.Send(SDKEventID.OnPurchaseProgressing);
                Log.w("In Purchase progressing");
                return;
            }

            if (m_Controller == null)
            {
                Log.e("Purchase Not Init ");
                return;
            }

            if (m_Controller.products.WithID(id) == null)
            {
                Log.e("No Product has ID" + id);
                return;
            }

            m_IsPurchaseInProgress = true;
            m_CurrentPurchasingID = id;
            m_Controller.InitiatePurchase(m_Controller.products.WithID(id));
            DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_REQUEST, DAPE.ALL, id);

            //防止卡死
            CleanCancelTimer();
            CleanValidateTimer();
            m_PurchaseCancelTimer = Timer.S.Post2Scale(OnPurchaseCancelTimeout, 20);
        }

        void OnPurchaseCancelTimeout(int count)
        {
            m_IsPurchaseInProgress = false;
            Timer.S.Cancel(m_PurchaseCancelTimer);
            m_PurchaseCancelTimer = -1;
        }

        void OnValidTimeout(int count)
        {
            m_IsPurchaseInProgress = false;
            EventSystem.S.Send(SDKEventID.OnPurchaseValidateTimeout, m_CurrentPurchasingID);
            Timer.S.Cancel(m_PurchaseValidTimer);
            m_PurchaseValidTimer = -1;
        }

        void CleanCancelTimer()
        {
            if (m_PurchaseCancelTimer > 0)
            {
                Timer.S.Cancel(m_PurchaseCancelTimer);
                m_PurchaseCancelTimer = -1;
            }
        }

        void CleanValidateTimer()
        {
            if (m_PurchaseValidTimer > 0)
            {
                Timer.S.Cancel(m_PurchaseValidTimer);
                m_PurchaseValidTimer = -1;
            }
        }

        public SubscriptionInfo GetSubscriptionManager(TDPurchase data)
        {
            if (data.productType == 2)
            {
                string key = "";
#if UNITY_IOS
                key = data.iOSKey;
#elif UNITY_ANDROID
                key = data.androidKey;
#endif
                if (SubscriptionManagerDic.ContainsKey(key))
                {
                    // Debug.Log("product id is: " + info.getProductId());
                    // Debug.Log("purchase date is: " + info.getPurchaseDate());
                    // Debug.Log("subscription next billing date is: " + info.getExpireDate());
                    // Debug.Log("is subscribed? " + info.isSubscribed().ToString());
                    // Debug.Log("is expired? " + info.isExpired().ToString());
                    // Debug.Log("is cancelled? " + info.isCancelled());
                    // Debug.Log("product is in free trial peroid? " + info.isFreeTrial());
                    // Debug.Log("product is auto renewing? " + info.isAutoRenewing());
                    // Debug.Log("subscription remaining valid time until next billing date is: " + info.getRemainingTime());
                    // Debug.Log("is this product in introductory price period? " + info.isIntroductoryPricePeriod());
                    // Debug.Log("the product introductory localized price is: " + info.getIntroductoryPrice());
                    // Debug.Log("the product introductory price period is: " + info.getIntroductoryPricePeriod());
                    // Debug.Log("the number of product introductory price period cycles is: " + info.getIntroductoryPricePeriodCycles());
                    return SubscriptionManagerDic[key];
                }
                else
                {
                    Log.w("this item is not a Subscription : " + key);
                    return null;
                }
            }

            return null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="callback"></param>
        /// <returns></returns>
        public bool Restore(Action<bool> callback)
        {
            if (m_IsGooglePlayStoreSelected)
            {
                // m_GooglePlayStoreExtensions.RestoreTransactions(callback);
            }
            else
            {
                if (Application.platform != RuntimePlatform.IPhonePlayer)
                {
                    return false;
                }

                // m_AppleExtensions.RestoreTransactions(callback);
            }

            return true;
        }


        public void OnInitializeFailed(InitializationFailureReason error)
        {
            switch (error)
            {
                case InitializationFailureReason.AppNotKnown:
                    Debug.LogError("Is your App correctly uploaded on the relevant publisher console?");
                    break;
                case InitializationFailureReason.PurchasingUnavailable:
                    // Ask the user if billing is disabled in device settings.
                    Debug.Log("Billing disabled!");
                    break;
                case InitializationFailureReason.NoProductsAvailable:
                    // Developer configuration error; check product metadata.
                    Debug.Log("No products available for purchase!");
                    break;
            }
            Log.i("purchase OnInitializeFailed");
            ThreadMgr.S.mainThread.PostAction(() =>
            {
                Timer.S.Post2Really(ReInitPurchaseInfo, 10);
                //DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_PRODUCT_FAILED);
            });

        }

        private void ReInitPurchaseInfo(int v)
        {
            Log.i("purchase OnInitializeFailed ReInitPurchaseInfo");
            Init();
        }

        /// <summary>
        /// 内购成功
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs e)
        {
            if (string.IsNullOrEmpty(m_CurrentPurchasingID))
            {
                return PurchaseProcessingResult.Complete;
            }

            try
            {
                //退出支付取消的计时,开启验证计时
                CleanCancelTimer();
                m_PurchaseValidTimer = Timer.S.Post2Really(OnValidTimeout, 10);
                m_PurchasedProduct = e.purchasedProduct;

                var item = e.purchasedProduct;

                if (item.definition.type == ProductType.Subscription)
                {

                    Log.i("purchase type" + item.definition.type);

                    Dictionary<string, string> introductory_info_dict = m_AppleExtensions?.GetIntroductoryPriceDictionary();
                    if (checkIfProductIsAvailableForSubscriptionManager(item.receipt))
                    {
                        string intro_json = (introductory_info_dict == null || !introductory_info_dict.ContainsKey(item.definition.storeSpecificId)) ? null : introductory_info_dict[item.definition.storeSpecificId];
                        Debug.Log("intro_json" + intro_json);
                        SubscriptionManager p = new SubscriptionManager(item, intro_json);
                        SubscriptionInfo info = p.getSubscriptionInfo();
                        SubscriptionManagerDic.Add(info.getProductId(), info);
                        Debug.Log("purchase info" + info?.ToString());
                    }
                    else
                    {
                        Debug.Log("This product is not available for SubscriptionManager class, only products that are purchase by 1.19+ SDK can use this class.");
                    }
                }
                else
                {
                    //Log.i("CurrentPurchase not a sub");
                }

                ValidateReceipt(e);
                Log.i("ProcessPurchase complete");
                m_Controller.ConfirmPendingPurchase(m_PurchasedProduct);

            }
            catch (Exception exception)
            {
                Debug.LogError("purchase" + exception.Message);
                throw;
            }

            return PurchaseProcessingResult.Complete;
        }

        public void OnPurchaseFailed(Product i, PurchaseFailureReason p)
        {
            CleanCancelTimer();
            m_IsPurchaseInProgress = false;
            m_CurrentPurchasingID = null;

            if (p == PurchaseFailureReason.UserCancelled)
            {
                Log.i("purchase failed UserCancelled");
                DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_CANCEL, DAPE.ALL, m_CurrentPurchasingID);
            }
            else
            {
                Log.i("purchase failed p" + p.ToString());
                DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_FAILED, DAPE.ALL, m_CurrentPurchasingID);
            }
        }

        public void OnInitialized(IStoreController controller, IExtensionProvider extensions)
        {
            Log.i("purchase OnInitialized");
            m_Controller = controller;
            var collections = controller.products.all;


            var datalist = TDPurchaseTable.dataList;
            for (int i = 0; i < datalist.Count; i++)
            {
                datalist[i].localPriceString = controller.products.WithID(datalist[i].id).metadata.localizedPriceString;
            }

            m_GooglePlayStoreExtensions = extensions as IGooglePlayStoreExtensions;
            m_AppleExtensions = extensions.GetExtension<IAppleExtensions>();
            m_AppleExtensions.RegisterPurchaseDeferredListener(OnDeferred);
            Dictionary<string, string> introductory_info_dict = m_AppleExtensions.GetIntroductoryPriceDictionary();
            foreach (var item in controller.products.all)
            {
                //#if INTERCEPT_PROMOTIONAL_PURCHASES
                //                // Set all these products to be visible in the user's App Store according to Apple's Promotional IAP feature
                //                // https://developer.apple.com/library/content/documentation/NetworkingInternet/Conceptual/StoreKitGuide/PromotingIn-AppPurchases/PromotingIn-AppPurchases.html
                //                m_AppleExtensions.SetStorePromotionVisibility(item, AppleStorePromotionVisibility.Show);
                //#endif

                if (item.definition.payouts != null)
                {
                    m_PurchasedKeysList.Add(item.definition.id);
                }

                // this is the usage of SubscriptionManager class
                if (item.receipt != null)
                {
                    if (item.definition.type == ProductType.Subscription)
                    {
                        if (checkIfProductIsAvailableForSubscriptionManager(item.receipt))
                        {
                            string intro_json = (introductory_info_dict == null || !introductory_info_dict.ContainsKey(item.definition.storeSpecificId)) ? null : introductory_info_dict[item.definition.storeSpecificId];
                            SubscriptionManager p = new SubscriptionManager(item, intro_json);
                            SubscriptionInfo info = p.getSubscriptionInfo();
                            SubscriptionManagerDic.Add(info.getProductId(), info);
                            Debug.Log("Purchase _ if " + info.getProductId());
                        }
                        else
                        {
                            Debug.Log("This product is not available for SubscriptionManager class, only products that are purchase by 1.19+ SDK can use this class.");
                        }
                    }
                    else
                    {
                        Debug.Log("the product is not a subscription product");
                    }
                }
                else
                {
                    Debug.Log("the product should have a valid receipt" + item);
                }

            }

            if (SubscriptionManagerDic.Count > 0)
            {
                EventSystem.S.Send(SDKEventID.OnSubscriptionLoaded);
            }
            EventSystem.S.Send(SDKEventID.OnPurchaseInitSuccess);
        }

        private void OnDeferred(Product obj)
        {
            Log.i("Purchase deferred: " + obj.definition.id);
        }

        #region validation
        void ValidateReceipt(PurchaseEventArgs e)
        {
            PurchaseAdapter.UnityIapBody body =
                LitJson.JsonMapper.ToObject<PurchaseAdapter.UnityIapBody>(e.purchasedProduct.receipt);
#if UNITY_ANDROID
            PurchaseAdapter.UnityIapPayloads pload = JsonMapper.ToObject<PurchaseAdapter.UnityIapPayloads>(body.Payload);
            // string publicKey, string purchaseData, string signature, string price, string currency, Dictionary<string,string> extraParams)
            AppsFlyerSDK.AppsFlyerAndroid.validateAndSendInAppPurchase(m_GPPublicAPIKey, pload.signature, pload.json,
                e.purchasedProduct.metadata.localizedPriceString, e.purchasedProduct.metadata.isoCurrencyCode, null, AppsFlyerInstance.S);
#elif UNITY_IOS
            var basePath = PurchaseDefine.IOS_PRODUCT_VERIFY_URL;
            if (Debug.isDebugBuild)
            {
                basePath = PurchaseDefine.IOS_SANDBOX_VERIFY_URL;
                AppsFlyeriOS.setUseReceiptValidationSandbox(true);
            }

            var mydata = new Boo.Lang.Hash();
            mydata["receipt-data"] = body.Payload;
            RestClient.Request(new RequestHelper
            {
                Uri = basePath,
                Method = "POST",
                Timeout = 10,
                BodyString = LitJson.JsonMapper.ToJson(mydata),
                ContentType = "application/json",
            }).Then(response =>
            {
                OnPurchaseValidResponse(response);
            });
#endif
        }

        public void OnValidationCompletion(int key, params object[] args)
        {
            CleanValidateTimer();
            if (args != null && args.Length > 0)
            {
                bool succ = (bool)args[0];
                if (succ)
                {
                    DoPurchaseSuccessTask();
                }
                else if (args.Length > 1)
                {
                    // DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_VALID_ERROR, args[1].ToString());
                    EventSystem.S.Send(SDKEventID.OnPurchaseFailed);
                }
            }
            m_IsPurchaseInProgress = false;
            m_CurrentPurchasingID = null;
        }

        void OnPurchaseValidResponse(ResponseHelper response)
        {
#if UNITY_IOS
            if (response.StatusCode == 200 && response.Text.Contains("\"status\":"))
            {
                var data = LitJson.JsonMapper.ToObject<PurchaseAdapter.ResponseBody_AppleReceipt>(response.Text);
                if (data.status == 0)
                {
                    EventSystem.S.Send(SDKEventID.OnPurchaseValidated, true);
                    //string productIdentifier, string price, string currency, string transactionId, Dictionary<string,string> additionalParametes
                    AppsFlyeriOS.validateAndSendInAppPurchase(m_PurchasedProduct.definition.id, m_PurchasedProduct.metadata.localizedPriceString,
                        m_PurchasedProduct.metadata.isoCurrencyCode, m_PurchasedProduct.transactionID, new Dictionary<string, string>(), AppsFlyerInstance.S);
                }
                else
                    EventSystem.S.Send(SDKEventID.OnPurchaseValidated, false, "status:" + data.status);
            }
            else
            {
                EventSystem.S.Send(SDKEventID.OnPurchaseValidated, false, response.Text);
            }
            //
#endif
        }
        #endregion

        void DoPurchaseSuccessTask()
        {
            TDPurchase data = TDPurchaseTable.GetData(m_CurrentPurchasingID);
            if (data == null)
            {
                Log.e("Invalid Config Key: " + m_CurrentPurchasingID);
                return;
            }

            DataAnalysisMgr.S.Pay(DAPE.ALL, (float)data.price / 100, data.itemNum);
            DataAnalysisMgr.S.CustomEvent(DataAnalysisDefine.PURCHASE_SUCCESS, DAPE.ALL, data.id);
            EventSystem.S.Send(SDKEventID.OnPurchaseSuccess, data, m_PurchasedProduct);

            PlayerPrefs.SetInt("PurchaseUserTag", 1);
        }

        [Serializable]
        public class UnityChannelPurchaseError
        {
            public string error;
            public UnityChannelPurchaseInfo purchaseInfo;
        }

        [Serializable]
        public class UnityChannelPurchaseInfo
        {
            public string productCode; // Corresponds to storeSpecificId
            public string gameOrderId; // Corresponds to transactionId
            public string orderQueryToken;
        }
    }
}

