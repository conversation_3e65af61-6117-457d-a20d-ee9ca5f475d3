# Unity IAP 4.x 到 5.0 升级说明

## 概述
本次升级将 UnityIapMgr 从 Unity IAP 4.x 升级到 5.0 版本，保持原有的公共接口不变，确保现有调用代码无需修改。

## 主要变化

### 1. 初始化方式变更
**4.x 版本:**
```csharp
var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());
UnityPurchasing.Initialize(this, builder);
```

**5.0 版本:**
```csharp
m_Controller = UnityIAPServices.StoreController();
await m_Controller.Connect();
```

### 2. 接口实现变更
**4.x 版本:**
- 实现 `IStoreListener` 接口
- 使用 `OnInitialized()`, `ProcessPurchase()`, `OnPurchaseFailed()` 方法

**5.0 版本:**
- 移除 `IStoreListener` 接口
- 使用事件驱动模式：`OnPurchasePending`, `OnPurchaseConfirmed`, `OnPurchaseFailed`

### 3. 产品获取方式变更
**4.x 版本:**
```csharp
m_Controller.products.WithID(productId)
m_Controller.products.all
```

**5.0 版本:**
```csharp
m_Controller.GetProducts().FirstOrDefault(p => p.definition.id == productId)
m_Controller.GetProducts()
```

### 4. 购买流程变更
**4.x 版本:**
```csharp
m_Controller.InitiatePurchase(product)
```

**5.0 版本:**
```csharp
m_Controller.PurchaseProduct(productId)
```

### 5. 扩展服务访问变更
**4.x 版本:**
```csharp
m_AppleExtensions = extensions.GetExtension<IAppleExtensions>();
m_GooglePlayStoreExtensions = extensions as IGooglePlayStoreExtensions;
```

**5.0 版本:**
```csharp
m_AppleExtensions = m_Controller.AppleStoreExtendedPurchaseService;
m_GooglePlayStoreExtensions = m_Controller.GooglePlayStoreExtendedPurchaseService;
```

### 6. 恢复购买变更
**4.x 版本:**
```csharp
m_AppleExtensions.RestoreTransactions(OnRestore);
m_GooglePlayStoreExtensions.RestoreTransactions(OnRestore);
```

**5.0 版本:**
```csharp
m_Controller.RestoreTransactions(OnRestore);
```

## 保持不变的公共接口

以下公共方法保持原有签名和功能不变：

- `Init()` - 初始化IAP
- `DoPurchase(TDPurchase data)` - 执行购买
- `DoPurchase(string productId)` - 执行购买
- `GetProductById(string id)` - 获取产品信息
- `IsItemReceipt(string productId)` - 检查是否有收据
- `IsSubscribedTo(string productId)` - 检查订阅状态
- `Restore()` - 恢复购买
- `AddCustomParam2PurchaseSuccEvt()` - 添加自定义参数

## 新增功能

### 1. 权益检查
新增 `OnCheckEntitlement` 事件处理，用于处理订阅权益检查。

### 2. 改进的错误处理
更详细的错误信息和状态回调。

## 测试验证

1. **编译测试**: 确保代码编译无错误
2. **初始化测试**: 验证IAP初始化流程
3. **购买测试**: 验证购买流程正常工作
4. **恢复测试**: 验证恢复购买功能
5. **事件测试**: 验证所有事件正常触发

## 注意事项

1. **异步初始化**: IAP 5.0 使用异步初始化，需要等待连接完成
2. **事件驱动**: 新版本完全基于事件驱动，需要正确设置事件处理器
3. **产品定义**: ProductDefinition 构造函数参数顺序可能有变化
4. **收据验证**: 收据验证逻辑保持兼容，但内部实现有所调整

## 兼容性

- **向后兼容**: 所有现有的调用代码无需修改
- **功能兼容**: 所有原有功能保持不变
- **性能优化**: 新版本在性能和稳定性方面有所提升

## 升级步骤

1. 确保Unity IAP包已升级到5.0版本
2. 替换 UnityIapMgr.cs 文件
3. 编译项目，检查是否有错误
4. 运行测试脚本验证功能
5. 在真机上测试购买流程

## 故障排除

如果遇到问题，请检查：
1. Unity IAP包版本是否正确
2. 产品配置是否正确
3. 平台设置是否正确
4. 网络连接是否正常
