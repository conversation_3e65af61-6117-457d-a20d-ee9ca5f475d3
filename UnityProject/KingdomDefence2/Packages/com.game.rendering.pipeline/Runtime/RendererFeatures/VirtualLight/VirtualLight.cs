using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

namespace Game.Core.Rendering
{
    [RequireComponent(typeof(MeshRenderer))]
    [RequireComponent(typeof(MeshFilter))]
    [ExecuteAlways]
    public class VirtualLight : MonoBehaviour
    {
        [SerializeField] private MeshRenderer m_MeshRenderer;
        [SerializeField] private MeshFilter m_MeshFilter;

        public LightType LightType = LightType.Point;
        public float Radius = 1;
        [Range(0, 1)] public float Intensity = 1;
        [ColorUsage(false, false)] public Color Color = Color.white;


        public Vector2 fallout = new Vector2(1, 1);

        private MaterialPropertyBlock m_MPB;

        static class ShaderConstants
        {
            public static int _Color = Shader.PropertyToID("_Color");
            public static int _Intensity = Shader.PropertyToID("_Intensity");
            public static int _Params = Shader.PropertyToID("_Params");
        }


        private void OnEnable()
        {
            m_MPB = new MaterialPropertyBlock();
        }

        private void Update()
        {
            m_MPB.SetColor(ShaderConstants._Color, Color);
            m_MPB.SetFloat(ShaderConstants._Intensity, Intensity);
            m_MPB.SetVector(ShaderConstants._Params, fallout);
            m_MeshRenderer.SetPropertyBlock(m_MPB);
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (m_MeshRenderer == null) m_MeshRenderer = GetComponent<MeshRenderer>();
            if (m_MeshFilter == null) m_MeshFilter = GetComponent<MeshFilter>();
        }

        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color;
            Gizmos.DrawWireSphere(transform.position, Radius * 2);
        }
#endif
    }
}