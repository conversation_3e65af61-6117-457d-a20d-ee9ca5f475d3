using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;

namespace Game.Core.Rendering
{
    public class IceEffectPass : ScriptableRenderPass
    {
        ProfilingSampler m_ProfilingSampler = new ProfilingSampler("IceEffectPass");
        private PipelineRendererFeature.MegaEffectSettings m_MegaEffectSettings;
        PassData m_PassData;
        internal FilteringSettings m_FilteringSettings;

        ShaderTagId[] forwardOnlyShaderTagIds = new ShaderTagId[]
        {
            new ShaderTagId("UniversalForward")
        };

        public IceEffectPass(PipelineRendererFeature.MegaEffectSettings megaEffectSettings)
        {
            m_MegaEffectSettings = megaEffectSettings;

            m_FilteringSettings = new FilteringSettings(RenderQueueRange.opaque, megaEffectSettings.IceLayerMask, MegaEffect<IceEffect>.S.renderingLayerMask);

            m_PassData = new PassData();
            m_PassData.m_ProfilingSampler = m_ProfilingSampler;
            m_PassData.pass = this;
            m_PassData.m_OverrideMaterial = megaEffectSettings.IceMaterial;
            m_PassData.m_ShaderTagIdList = new();
            foreach (ShaderTagId sid in forwardOnlyShaderTagIds)
                m_PassData.m_ShaderTagIdList.Add(sid);
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            if (!m_MegaEffectSettings.EnableIce /*|| m_Settings.AfterImageMaterial == null*/)
                return;

            m_PassData.m_RenderingData = renderingData;
            // m_FilteringSettings.renderingLayerMask = m_Settings.IceRenderingLayerMask;
            m_PassData.m_FilteringSettings = m_FilteringSettings;


            ExecutePass(context, m_PassData, ref renderingData, renderingData.cameraData.IsCameraProjectionMatrixFlipped());
        }

        private class PassData
        {
            internal RenderingData m_RenderingData;
            internal Material m_OverrideMaterial;
            internal RenderStateBlock m_RenderStateBlock;
            internal FilteringSettings m_FilteringSettings;
            internal List<ShaderTagId> m_ShaderTagIdList;
            internal ProfilingSampler m_ProfilingSampler;

            internal IceEffectPass pass;
        }

        private static void ExecutePass(ScriptableRenderContext context, PassData data, ref RenderingData renderingData, bool yFlip)
        {
            var cmd = CommandBufferPool.Get("IceEffect");
            using (new ProfilingScope(cmd, data.m_ProfilingSampler))
            {
                DrawingSettings drawSettings = RenderingUtils.CreateDrawingSettings(data.m_ShaderTagIdList, ref renderingData, renderingData.cameraData.defaultOpaqueSortFlags);
                drawSettings.overrideMaterial = data.m_OverrideMaterial;
                context.DrawRenderers(renderingData.cullResults, ref drawSettings, ref data.m_FilteringSettings);
            }

            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }
    }
}