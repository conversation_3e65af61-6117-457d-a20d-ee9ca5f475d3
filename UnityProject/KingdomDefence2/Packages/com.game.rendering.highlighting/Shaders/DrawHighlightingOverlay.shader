Shader "Hidden/DrawHighlightingOverlay"
{
    Properties
    {
        _MainTex ("MainTex", 2D) = "white" { }

    }
    
    SubShader
    {
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
        #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/SpaceTransforms.hlsl"

        struct MeshAttributes
        {
            float4 positionOS : POSITION;
            UNITY_VERTEX_INPUT_INSTANCE_ID
        };

        struct MeshVaryings
        {
            float4 positionCS : SV_POSITION;
        };

        MeshVaryings OverlayOutlineVert(MeshAttributes input)
        {
            MeshVaryings output = (MeshVaryings)0;
            UNITY_SETUP_INSTANCE_ID(input);

            half3 positionOS = input.positionOS.xyz;
            
            float4 positionCS = TransformObjectToHClip(positionOS);
            output.positionCS = positionCS;

            return output;
        }

        ENDHLSL

        Tags { "RenderPipeline" = "UniversalPipeline" "RenderType" = "Opaque" }

        // #0: things that are visible (pass depth). 1 in alpha, 1 in red (SM3.0)
        Pass
        {
            NAME "Success depth"
            Tags { "LightMode" = "UniversalForward" }
            Blend One Zero
            ZTest LEqual
            Cull Off
            ZWrite Off
            // push towards camera a bit, so that coord mismatch due to dynamic batching is not affecting us
            Offset -0.02, 0
            
            HLSLPROGRAM
            
            #pragma vertex OverlayOutlineVert
            #pragma fragment OverlayOutlineFrag

            half4 OverlayOutlineFrag(MeshVaryings input) : SV_Target
            {
                return half4(0.5, 1, 1, 1);
            }
            
            ENDHLSL
        }

        // #1: all the things, including the ones that fail the depth test. Additive blend, 1 in green, 1 in alpha
        Pass
        {
            NAME "Fail depth"
            Tags { "LightMode" = "UniversalForward" }
            Blend One One
            BlendOp Max
            ZTest Always
            ZWrite Off
            Cull Off
            ColorMask GBA
            // push towards camera a bit, so that coord mismatch due to dynamic batching is not affecting us
            Offset -0.02, 0
            
            HLSLPROGRAM
            
            #pragma vertex OverlayOutlineVert
            #pragma fragment OverlayOutlineFrag

            half4 OverlayOutlineFrag(MeshVaryings input) : SV_Target
            {
                return half4(0, 0, 1, 1);
            }
            
            ENDHLSL
        }


        //2 Overlay Blur
        Pass
        {
            Name "Overlay Blur"
            Tags { "LightMode" = "UniversalForward" }
            
            Cull Off
            
            HLSLPROGRAM
            
            #pragma vertex BlurVert
            #pragma fragment BlurFrag

            #include "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/Common.hlsl"

            float4 _BlitTexture_TexelSize;
            float _OffsetScale;

            struct BlurVaryings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4 uv12 : TEXCOORD1;
                float4 uv34 : TEXCOORD2;
            };

            BlurVaryings BlurVert(Attributes input)
            {
                BlurVaryings output = (BlurVaryings)0;
            #if SHADER_API_GLES
                float4 pos = input.positionOS;
                float2 uv = input.uv;
            #else
                float4 pos = GetFullScreenTriangleVertexPosition(input.vertexID);
                float2 uv = GetFullScreenTriangleTexCoord(input.vertexID);
            #endif
                output.positionCS = pos;
                output.uv = uv;

                float2 uv1, uv2, uv3, uv4;
                float2 temp = uv;

                _BlitTexture_TexelSize *= 0.5;
                float _Offset = _OffsetScale;
                output.uv12.xy = output.uv - _BlitTexture_TexelSize.xy * float2(1 + _Offset, 1 + _Offset); //top right
                output.uv12.zw = output.uv + _BlitTexture_TexelSize.xy * float2(1 + _Offset, 1 + _Offset); //bottom left
                output.uv34.xy = output.uv - float2(_BlitTexture_TexelSize.x, -_BlitTexture_TexelSize.y) * float2(1 + _Offset, 1 + _Offset); //top left
                output.uv34.zw = output.uv + float2(_BlitTexture_TexelSize.x, -_BlitTexture_TexelSize.y) * float2(1 + _Offset, 1 + _Offset); //bottom right

                return output;
            }

            half4 BlurFrag(BlurVaryings input) : SV_Target
            {
                float4 color1 = SAMPLE_TEXTURE2D(_BlitTexture, sampler_LinearClamp, input.uv12.xy);
                float4 color2 = SAMPLE_TEXTURE2D(_BlitTexture, sampler_LinearClamp, input.uv12.zw);
                float4 color3 = SAMPLE_TEXTURE2D(_BlitTexture, sampler_LinearClamp, input.uv34.xy);
                float4 color4 = SAMPLE_TEXTURE2D(_BlitTexture, sampler_LinearClamp, input.uv34.zw);
                float4 color = color1 + color2 + color3 + color4;
                color *= 0.25;
                // color = step(color, 0.1);
                return color;
            }
            
            ENDHLSL
        }

        //3 Overlay Composite
        Pass
        {
            Name "Overlay Composite"
            Tags { "LightMode" = "UniversalForward" }
            
            Cull Off
            
            HLSLPROGRAM
            
            #pragma vertex Vert
            #pragma fragment CompositeFrag

            #include "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/Common.hlsl"

            float4 _BlitTexture_TexelSize;
            half4 _Color;

            int _Type;

            TEXTURE2D(_HighlightingRT);
            TEXTURE2D(_HighlightingBlurRT);

            half4 CompositeFrag(Varyings input) : SV_Target
            {
                
                float2 uv = input.texcoord.xy;
                float4 highResult = SAMPLE_TEXTURE2D(_HighlightingRT, sampler_LinearClamp, uv) * 10;
                highResult = saturate(highResult);

                float backMask = saturate(highResult.b - highResult.r);
                // return backMask;
                
                
                float4 blurResult = SAMPLE_TEXTURE2D(_HighlightingBlurRT, sampler_LinearClamp, uv);
                // return saturate(saturate(highResult.b * 10) - saturate(blurResult.r * 10)) ;
                // return (blurResult.r - highResult.r) ;

                float4 color = SAMPLE_TEXTURE2D(_BlitTexture, sampler_LinearClamp, uv);
                // return saturate(step(0.1, blurResult - highResult));
                
                //重合部分去掉
                if (_Type == 0)
                    return lerp(color, _Color, saturate(step(0.1, (blurResult.r - highResult.b))));
                else
                //重合部分也显示
                return lerp(color, _Color, saturate(step(0.1, (blurResult.r - highResult.r))));
            }
            
            ENDHLSL
        }

        //4 Overlay Composite
        Pass
        {
            Name "Overlay Composite"
            Tags { "LightMode" = "UniversalForward" }
            
            Cull Off
            
            HLSLPROGRAM
            
            #pragma vertex Vert
            #pragma fragment CompositeFrag

            #include "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/Common.hlsl"
            TEXTURE2D(_MainTex);
            float4 _MainTex_TexelSize;
            half4 _Color;

            TEXTURE2D(_HighlightingRT);
            TEXTURE2D(_HighlightingBlurRT);

            static const half2 kOffsets[8] = {
                half2(-1, -1),
                half2(0, -1),
                half2(1, -1),
                half2(-1, 0),
                half2(1, 0),
                half2(-1, 1),
                half2(0, 1),
                half2(1, 1)
            };


            half4 CompositeFrag(Varyings input) : SV_Target
            {
                float2 uv = input.texcoord.xy;
                half4 currentTexel = SAMPLE_TEXTURE2D(_MainTex, sampler_LinearClamp, uv);
                float alpha = 0;

                //使用g通道判断前后遮挡关系
                //在上一个shader中，被遮挡的g值为0
                bool isFront = currentTexel.g > 0.0;

                //遍历当前像素周边8个像素。
                for (int tap = 0; tap < 8; ++tap)
                {
                    float id = SAMPLE_TEXTURE2D(_MainTex, sampler_LinearClamp, uv + (kOffsets[tap] * _MainTex_TexelSize.xy)).b;
                    //如果出现b值差（选中物体的渲染区域的b值为1，未渲染的其他地方b值为0）
                    //则此像素为“边缘”，进行alpha赋值
                    if (id - currentTexel.b != 0)
                    {
                        alpha = 0.9;
                        if (!isFront)
                        {
                            alpha = 0.3;
                        }
                    }
                }
                //返回一个颜色，与原本的屏幕颜色进行alpha颜色混合
                float4 UnityOutlineColor = float4(1, 0, 0, alpha);
                return UnityOutlineColor;
            }
            
            ENDHLSL
        }
    }
    FallBack "Diffuse"
}