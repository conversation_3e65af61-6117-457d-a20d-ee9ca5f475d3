using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace Game.Core.Highlighting.Editor
{
    [CustomEditor(typeof(HighlightingRendererFeature))]
    public class HighlightingRendererFeatureEditor : UnityEditor.Editor
    {
        SerializedProperty m_Settings;

        SerializedProperty m_HighlightingType;
        SerializedProperty m_AdaptiveThickness;
        SerializedProperty m_Thickness;
        SerializedProperty m_Color;
        SerializedProperty m_BlurStrength;
        SerializedProperty m_RenderPassEvent;
        SerializedProperty m_OverlayType;
        SerializedProperty m_DownsampleMode;

        void OnEnable()
        {
            m_Settings = serializedObject.FindProperty("m_Settings");
            m_HighlightingType = m_Settings.FindPropertyRelative("highlightingType");
            m_AdaptiveThickness = m_Settings.FindPropertyRelative("_AdaptiveThickness");
            m_Thickness = m_Settings.FindPropertyRelative("_Thickness");
            m_Color = m_Settings.FindPropertyRelative("_Color");
            m_BlurStrength = m_Settings.FindPropertyRelative("blurStrength");
            m_RenderPassEvent = m_Settings.FindPropertyRelative("renderPassEvent");
            m_OverlayType = m_Settings.FindPropertyRelative("overlayType");
            m_DownsampleMode = m_Settings.FindPropertyRelative("downsampleMode");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            EditorGUILayout.PropertyField(m_RenderPassEvent);
            EditorGUILayout.PropertyField(m_HighlightingType);
            EditorGUILayout.Space(5);
            // m_HighlightingType.enumValueIndex = (int)(HighlightingType)EditorGUILayout.EnumPopup("HighlightingType:", (HighlightingType)m_HighlightingType.enumValueIndex);
            switch (m_HighlightingType.enumValueIndex)
            {
                case (int)HighlightingType.Mesh:
                    EditorGUILayout.PropertyField(m_AdaptiveThickness);
                    EditorGUILayout.PropertyField(m_Thickness);
                    EditorGUILayout.PropertyField(m_Color);
                    break;
                case (int)HighlightingType.Overlay:
                    EditorGUILayout.PropertyField(m_OverlayType);
                    EditorGUILayout.PropertyField(m_DownsampleMode);
                    EditorGUILayout.PropertyField(m_BlurStrength);
                    EditorGUILayout.PropertyField(m_Color);
                    break;
            }
            serializedObject.ApplyModifiedProperties();
        }

    }
}
