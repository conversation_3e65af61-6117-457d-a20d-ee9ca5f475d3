{"dependencies": {"com.coffee.ui-particle": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "com.greenbamboogames.assetquickaccess": "**************:SolarianZ/UnityAssetQuickAccessTool.git", "com.kyrylokuzyk.primetween": "file:../Assets/Plugins/PrimeTween/internal/com.kyrylokuzyk.primetween.tgz", "com.unity.2d.sprite": "1.0.0", "com.unity.addressables": "1.22.3", "com.unity.ai.navigation": "1.1.6", "com.unity.burst": "1.8.21", "com.unity.cinemachine": "2.10.3", "com.unity.collab-proxy": "2.7.1", "com.unity.collections": "2.5.7", "com.unity.device-simulator.devices": "1.0.0", "com.unity.formats.fbx": "4.2.1", "com.unity.ide.rider": "3.0.36", "com.unity.ide.visualstudio": "2.0.22", "com.unity.memoryprofiler": "1.1.6", "com.unity.mobile.notifications": "2.4.0", "com.unity.purchasing": "5.0.0", "com.unity.render-pipelines.universal": "14.0.12", "com.unity.splines": "2.8.0", "com.unity.test-framework": "1.1.33", "com.unity.textmeshpro": "3.0.9", "com.unity.timeline": "1.8.8", "com.unity.ugui": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}, "scopedRegistries": [{"name": "package.openupm.com", "url": "https://package.openupm.com", "scopes": ["com.openupm", "com.tayx.graphy"]}]}