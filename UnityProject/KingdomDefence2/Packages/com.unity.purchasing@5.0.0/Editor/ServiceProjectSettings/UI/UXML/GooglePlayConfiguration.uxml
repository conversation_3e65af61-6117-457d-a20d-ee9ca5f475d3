<UXML xmlns="UnityEngine.UIElements">
    <VisualElement name="GoogleConfigBlock" class="project-settings-title-container">
        <Label text="Google Play Configuration" class="project-settings-title"/>
        <VisualElement>
            <Label text="Please follow these steps to get your license key:" class="description"/>
            <VisualElement class="label-container">
                <Label text="1. Go to " class="numbered-item"/>
                <VisualElement name="GooglePlayLink" class="link-container-integrated">
                    <TextElement text="Google Play Developer Console" class="link-button"/>
                    <VisualElement class="external-link-icon"/>
                </VisualElement>
            </VisualElement>
            <Label text="2. Go to your appropriate project." class="numbered-item"/>
            <Label text="3. Go to &quot;Monetization setup&quot;, there you will find your License Key " class="numbered-item"/>
        </VisualElement>
        <VisualElement class="invisible-separator" />
        <VisualElement name="RevenueValidationBlock">
            <Label text="Revenue Validation" class="project-settings-subtitle"/>
            <Label text="Enter your Google Play key on your project's dashboard:" class="note-tag"/>
            <VisualElement name="DashboardSettingsLink" class="link-container-integrated">
                <TextElement text="Unity Project Settings in the dashboard" class="link-button"/>
                <VisualElement class="external-link-icon"/>
            </VisualElement>
            <VisualElement name="verified-mode">
                <Label text="Great! You will now be able to track verified transactions taking place on your app." class="description"/>
            </VisualElement>
            <VisualElement name="unverified-mode">
                <VisualElement name="error-request-format">
                    <Label text="The request to get the key on the dashboard was invalid. Please report this as a possible bug." class="warning-message"/>
                </VisualElement>
                <VisualElement name="error-unauthorized-user">
                    <Label text="You are not authorized to set the license key." class="warning-message"/>
                </VisualElement>
                <VisualElement name="error-server-error">
                    <Label text="There was an error retrieving the key from the backend. Please try again later." class="warning-message"/>
                </VisualElement>
                <VisualElement name="error-fetch-key">
                    <Label text="There seems to be no existing key set on the backend." class="warning-message"/>
                    <VisualElement class="warning-info-icon2x"/>
                    <Label text="Please verify the key in your Dashboard's Project Settings via the link above. Under In-App Purchasing Settings, set the Google License Key for this application, in order to ensure revenue numbers reflect verified payments for Google." class="warning-message"/>
                </VisualElement>
            </VisualElement>
        </VisualElement>
        <VisualElement class="invisible-separator" />
        <VisualElement name="ReceiptObfuscatorBlock">
            <Label text="Receipt Obfuscator" class="project-settings-subtitle"/>
            <Label text="Receipt obfuscation for Google Play will create a Tangle class in your project, note that this does not replace server side validation." class="label-container"/>
            <TextField name="GooglePlayKeyEntry" class="multiline-field-value" text="-Enter Key-" multiline="true"/>
            <VisualElement class="button-container">
                <Button name="ObfuscateKeysButton" text="Obfuscate License Keys" class="submit-button"/>
            </VisualElement>
            <VisualElement name="verification">
                <Label text="GooglePlayTangle.cs has been generated and exists in your project." class="label-container"/>
            </VisualElement>
            <VisualElement name="error-message">
                <Label text="-Google Errors Show Here-." class="warning-message"/>
            </VisualElement>
        </VisualElement>
    </VisualElement>
    <VisualElement class="separator" />
</UXML>
