#ifndef LOWPOLY_FORWARD_PASS_INCLUDE
#define LOWPOLY_FORWARD_PASS_INCLUDE

#include "Packages/com.game.rendering.pipeline/ShaderLibrary/LowpolyLighting.hlsl"
#if defined(LOD_FADE_CROSSFADE)
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
#endif

#ifdef _SNOW_ON
float3 GetNormalOSFromVertexColor(float4 vertexColor, float3 normalOS, float4 tangentOS)
{
    // #if _OUTLINETYPE_VERTEXCOLOR
    half3 normalTS = (vertexColor.xyz * 2) - 1;

    float sgn = tangentOS.w * GetOddNegativeScale();
    float3 bitangent = sgn * cross(normalOS.xyz, tangentOS.xyz);
    half3x3 tangentToObject = half3x3(tangentOS.xyz, bitangent.xyz, normalOS.xyz);
    // 注意这里矩阵是右乘
    normalOS = mul(normalTS, tangentToObject);
    // #endif

    normalOS = normalize(normalOS);
    return normalOS;
}
#endif

struct Attributes
{
    float4 positionOS : POSITION;
    float3 normalOS : NORMAL0;
    float2 texcoord : TEXCOORD0;
    #ifdef _SNOW_ON
    float4 tangentOS : TANGENT;
    float4 color : COLOR;
    #endif
    #if _USEPBR_ON
    float2 texcoord1 : TEXCOORD1;
    float2 texcoord2 : TEXCOORD2;
    #endif
    UNITY_VERTEX_INPUT_INSTANCE_ID
};

struct Varyings
{
    float4 positionCS : SV_POSITION;
    float3 normalWS : NORMAL0;
    float2 uv : TEXCOORD0;
    float3 positionWS : TEXCOORD1;
    float2 uv2 : TEXCOORD2;
    // float3 viewDirWS : TEXCOORD2;
    UNITY_VERTEX_INPUT_INSTANCE_ID
    UNITY_VERTEX_OUTPUT_STEREO
};

Varyings ForwardVert(Attributes input)
{
    Varyings output = (Varyings)0;
    UNITY_SETUP_INSTANCE_ID(input);
    UNITY_TRANSFER_INSTANCE_ID(input, output);
    UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);


    float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

    #ifdef _SNOW_ON
    half3 smoothNormalOS = GetNormalOSFromVertexColor(input.color, input.normalOS, input.tangentOS);
    float3 smoothNormalWS = TransformObjectToWorldNormal(smoothNormalOS);
    if (dot(smoothNormalWS, half3(0, 1, 0)) >= lerp(1, -1, (_SnowStrength * 2) / 3))
    {
        input.positionOS.xyz += smoothNormalOS * _SnowStrength * _SnowDepth;
    }
    #endif

    VertexPositionInputs vertexPos = GetVertexPositionInputs(input.positionOS.xyz);
    // output.viewDirWS = GetWorldSpaceViewDir(vertexPos.positionWS);
    output.normalWS = normalWS;
    output.positionCS = vertexPos.positionCS;
    output.positionWS = vertexPos.positionWS;
    output.uv = TRANSFORM_TEX(input.texcoord, _MainTex);
    #if _USEPBR_ON
    output.uv2 = float4(input.texcoord1.xy, input.texcoord1.xy);
    #endif


    return output;
}

void ForwardFrag(
    Varyings input
    , out half4 outColor : SV_Target0
    #ifdef _WRITE_RENDERING_LAYERS
    , out float4 outRenderingLayers : SV_Target1
    #endif
)
{
    UNITY_SETUP_INSTANCE_ID(input);
    UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

    #ifdef LOD_FADE_CROSSFADE
    LODFadeCrossFade(input.positionCS);
    #endif

    // Base Color
    half4 albedoAlpha = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);
    albedoAlpha.rgb *= _MainTexColor.rgb;

    #ifdef _ALPHATEST_ON
    clip(albedoAlpha.a - _Cutoff);
    #endif

    SurfaceData surfaceData = (SurfaceData)0;
    surfaceData.albedo = albedoAlpha.rgb;
    surfaceData.alpha = albedoAlpha.a * _MainTexColor.a;
    surfaceData.emission = _EmissionColor.rgb * _EmissionIntensity;
    surfaceData.clearCoatMask = _NIGHTCOLOR;

    #if _USEPBR_ON
    //金属度
    half4 pbr = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv2);
    surfaceData.metallic = pbr.r * _Metallic;
    surfaceData.smoothness = pbr.r * _Smoothness;
    #endif

    float3 normalWS = normalize(input.normalWS);

    #ifdef _SNOW_ON
    half4 snowTex = SAMPLE_TEXTURE2D(_SnowTex, sampler_SnowTex, input.positionWS.xz * _SnowTilling);
    float snowMask = dot(normalWS, float3(0, 1, 0));
    float minSnow = min(snowMask + _SnowSoftness, snowMask - _SnowSoftness);
    float maxSnow = max(snowMask + _SnowSoftness, snowMask - _SnowSoftness);
    snowMask = smoothstep(maxSnow, minSnow, _SnowStrength);

    surfaceData.albedo = lerp(surfaceData.albedo, _SnowColor * snowTex, snowMask);
    #endif

    InputData inputData = (InputData)0;
    inputData.normalWS = normalWS;
    inputData.positionWS = input.positionWS;
    inputData.normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
    half4 result = LowpolyLighting(inputData, surfaceData);

    #ifdef _GLOBALEFFECT_ON
    ApplyGlobalFog(input.positionWS, result.rgb);
    #endif

    outColor = result;

    #ifdef _WRITE_RENDERING_LAYERS
    uint renderingLayers = GetMeshRenderingLayer();
    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
    #endif
}

#endif
