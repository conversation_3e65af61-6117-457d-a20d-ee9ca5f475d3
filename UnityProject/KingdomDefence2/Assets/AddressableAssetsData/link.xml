<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Aya.DataBinding.AnimSliderBinder" preserve="all" />
    <type fullname="DG.Tweening.DOTweenAnimation" preserve="all" />
    <type fullname="DitzelGames.FastIK.FastIKFabric" preserve="all" />
    <type fullname="DoozyUI.UIEffect" preserve="all" />
    <type fullname="DoozyUI.UIElement" preserve="all" />
    <type fullname="GameWish.Game.AchievementPanel" preserve="all" />
    <type fullname="GameWish.Game.AddWoodTip" preserve="all" />
    <type fullname="GameWish.Game.AdRewardPanel" preserve="all" />
    <type fullname="GameWish.Game.AdRewardPanelItem" preserve="all" />
    <type fullname="GameWish.Game.AnimInstancing.GPU_Skinning" preserve="all" />
    <type fullname="GameWish.Game.ArchievementGroup" preserve="all" />
    <type fullname="GameWish.Game.ArrowTowerCtrller" preserve="all" />
    <type fullname="GameWish.Game.AxisRotateCtrller" preserve="all" />
    <type fullname="GameWish.Game.BarracksCtrller" preserve="all" />
    <type fullname="GameWish.Game.BaseRewardItem" preserve="all" />
    <type fullname="GameWish.Game.BattleBookPanel" preserve="all" />
    <type fullname="GameWish.Game.BattleBookPanel_Item" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffConfigSO" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffGroupCount" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffGroupCountItem" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffItem" preserve="all" />
    <type fullname="GameWish.Game.BattleBuffPanel" preserve="all" />
    <type fullname="GameWish.Game.BattleContinuePanel" preserve="all" />
    <type fullname="GameWish.Game.BattleFailPanel" preserve="all" />
    <type fullname="GameWish.Game.BattleFailRewardPanel" preserve="all" />
    <type fullname="GameWish.Game.BattleFailSuggestItem" preserve="all" />
    <type fullname="GameWish.Game.BattleHeroRevivePanel" preserve="all" />
    <type fullname="GameWish.Game.BattlePassItem" preserve="all" />
    <type fullname="GameWish.Game.BattlePassPanel" preserve="all" />
    <type fullname="GameWish.Game.BattlePassRewardItem" preserve="all" />
    <type fullname="GameWish.Game.BattlePausePanel" preserve="all" />
    <type fullname="GameWish.Game.BattlePausePanel_Item" preserve="all" />
    <type fullname="GameWish.Game.BattleSettlementRewardItem" preserve="all" />
    <type fullname="GameWish.Game.BattleSpawnAreaCtrller" preserve="all" />
    <type fullname="GameWish.Game.BattleStagePanel" preserve="all" />
    <type fullname="GameWish.Game.BattleVictoryPanel" preserve="all" />
    <type fullname="GameWish.Game.BoneFollow_Tool" preserve="all" />
    <type fullname="GameWish.Game.BottomBarItem" preserve="all" />
    <type fullname="GameWish.Game.BottomBarPanel" preserve="all" />
    <type fullname="GameWish.Game.BottomBgPanel" preserve="all" />
    <type fullname="GameWish.Game.BottomMaskPanel" preserve="all" />
    <type fullname="GameWish.Game.BuildingEffectContent" preserve="all" />
    <type fullname="GameWish.Game.BuildingFunctionConfigSO" preserve="all" />
    <type fullname="GameWish.Game.BuildingFunctionGroupConfigSO" preserve="all" />
    <type fullname="GameWish.Game.BuildingLockFrame" preserve="all" />
    <type fullname="GameWish.Game.BuildingLockItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingPropItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingSelectItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingSelectPanel" preserve="all" />
    <type fullname="GameWish.Game.BuildingSelectTip" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarContent" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetailAttrGroup" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetailBasicAttr" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetailSuperAttr" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetailTip" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetialAbilityItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarDetialSkillItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarPanel" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarRoot" preserve="all" />
    <type fullname="GameWish.Game.BuildingStarUpPanel" preserve="all" />
    <type fullname="GameWish.Game.BuildingUpgradeFrame" preserve="all" />
    <type fullname="GameWish.Game.BuildingUpgradeItem" preserve="all" />
    <type fullname="GameWish.Game.BuildingUpgradePanel" preserve="all" />
    <type fullname="GameWish.Game.CallbackTrigger" preserve="all" />
    <type fullname="GameWish.Game.CatapultBullet" preserve="all" />
    <type fullname="GameWish.Game.CDCircle" preserve="all" />
    <type fullname="GameWish.Game.ChapterPanel" preserve="all" />
    <type fullname="GameWish.Game.ChapterPanel_Box" preserve="all" />
    <type fullname="GameWish.Game.ChapterPanel_UnlockFuncTips" preserve="all" />
    <type fullname="GameWish.Game.ChapterPanel_UnlockHeroLvl" preserve="all" />
    <type fullname="GameWish.Game.CombineBuildingCtrller" preserve="all" />
    <type fullname="GameWish.Game.CombineSignPanel" preserve="all" />
    <type fullname="GameWish.Game.ConfirmDropRewardPanel" preserve="all" />
    <type fullname="GameWish.Game.ConfirmUpgradePanel" preserve="all" />
    <type fullname="GameWish.Game.ContiniousHitBullet" preserve="all" />
    <type fullname="GameWish.Game.CreateCircleCollider" preserve="all" />
    <type fullname="GameWish.Game.CrossPanel" preserve="all" />
    <type fullname="GameWish.Game.DebugBattlePanel" preserve="all" />
    <type fullname="GameWish.Game.DecalCtrller" preserve="all" />
    <type fullname="GameWish.Game.DefenceWallCtrller" preserve="all" />
    <type fullname="GameWish.Game.DirectMoveBullet" preserve="all" />
    <type fullname="GameWish.Game.DoubleCoinRevenuePanel" preserve="all" />
    <type fullname="GameWish.Game.DrawBoxProbilityPanel" preserve="all" />
    <type fullname="GameWish.Game.DropBagCtrller" preserve="all" />
    <type fullname="GameWish.Game.DropChestCtrller" preserve="all" />
    <type fullname="GameWish.Game.EnemyCtrller" preserve="all" />
    <type fullname="GameWish.Game.EnemyInfoPanel" preserve="all" />
    <type fullname="GameWish.Game.EnemyInfoPanel_Item" preserve="all" />
    <type fullname="GameWish.Game.EnemyLineCtrller" preserve="all" />
    <type fullname="GameWish.Game.FlyEnemyCtrller" preserve="all" />
    <type fullname="GameWish.Game.FuncUnlockPanel" preserve="all" />
    <type fullname="GameWish.Game.FuncUnlockPrefab" preserve="all" />
    <type fullname="GameWish.Game.GameGuideArrowCtrller" preserve="all" />
    <type fullname="GameWish.Game.GameGuideLineCtrller" preserve="all" />
    <type fullname="GameWish.Game.GamePlayerPower" preserve="all" />
    <type fullname="GameWish.Game.GamingPlayer" preserve="all" />
    <type fullname="GameWish.Game.GamingPropertyItem" preserve="all" />
    <type fullname="GameWish.Game.GemDropBoxCtrller" preserve="all" />
    <type fullname="GameWish.Game.GetObjectEntry" preserve="all" />
    <type fullname="GameWish.Game.GlobalSkillCastPanel" preserve="all" />
    <type fullname="GameWish.Game.GlobalSkillCDItem" preserve="all" />
    <type fullname="GameWish.Game.GlobalSkillSelectItem" preserve="all" />
    <type fullname="GameWish.Game.GlobalSkillSelectPanel" preserve="all" />
    <type fullname="GameWish.Game.GlobalSkillSlotItem" preserve="all" />
    <type fullname="GameWish.Game.GuardBladeBullet" preserve="all" />
    <type fullname="GameWish.Game.GuardBladeCtrller" preserve="all" />
    <type fullname="GameWish.Game.GuideDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.GuideEntranceUnlockPanel" preserve="all" />
    <type fullname="GameWish.Game.GuideJoystickPanel" preserve="all" />
    <type fullname="GameWish.Game.GuideUnlcokBtn" preserve="all" />
    <type fullname="GameWish.Game.GuideWordPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroAltarCtrller" preserve="all" />
    <type fullname="GameWish.Game.HeroBuyPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroCtrller" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_EquipItem" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_EquipRoot" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_EquipUpgrade" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_HeroPrefab" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_HeroRoot" preserve="all" />
    <type fullname="GameWish.Game.HeroEquipPanel_TreasureRoot" preserve="all" />
    <type fullname="GameWish.Game.HeroLvlLimitUpPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroLvlUpPanel_Stars" preserve="all" />
    <type fullname="GameWish.Game.HeroLvUpPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroLvUpPanel_Item" preserve="all" />
    <type fullname="GameWish.Game.HeroShowStage" preserve="all" />
    <type fullname="GameWish.Game.HeroTeamPanel" preserve="all" />
    <type fullname="GameWish.Game.HeroTeamPanelItem" preserve="all" />
    <type fullname="GameWish.Game.HintPosIcon" preserve="all" />
    <type fullname="GameWish.Game.HitStopRotBullet" preserve="all" />
    <type fullname="GameWish.Game.HurtPanel" preserve="all" />
    <type fullname="GameWish.Game.IcePitonBullet" preserve="all" />
    <type fullname="GameWish.Game.ItemDropCtrller" preserve="all" />
    <type fullname="GameWish.Game.JumpWallEnemyCtrller" preserve="all" />
    <type fullname="GameWish.Game.LivenessRewardItem" preserve="all" />
    <type fullname="GameWish.Game.LoggingCtrller" preserve="all" />
    <type fullname="GameWish.Game.MainHallCtrller" preserve="all" />
    <type fullname="GameWish.Game.MainHallPosIcon" preserve="all" />
    <type fullname="GameWish.Game.MissTip" preserve="all" />
    <type fullname="GameWish.Game.NoOverdrawImage" preserve="all" />
    <type fullname="GameWish.Game.NPCBubble" preserve="all" />
    <type fullname="GameWish.Game.NPCCtrller" preserve="all" />
    <type fullname="GameWish.Game.OnlinePanel" preserve="all" />
    <type fullname="GameWish.Game.OnlinePanel_Item" preserve="all" />
    <type fullname="GameWish.Game.OpenBoxPanelPanel" preserve="all" />
    <type fullname="GameWish.Game.ParabolaMoveBullet" preserve="all" />
    <type fullname="GameWish.Game.ParticleBulletCtrller" preserve="all" />
    <type fullname="GameWish.Game.PlantBullet" preserve="all" />
    <type fullname="GameWish.Game.PlayerLvlUpPanel" preserve="all" />
    <type fullname="GameWish.Game.PoisonCircleBullet" preserve="all" />
    <type fullname="GameWish.Game.PromotionHeroPackPanel" preserve="all" />
    <type fullname="GameWish.Game.PromotionNewbiePackPanel" preserve="all" />
    <type fullname="GameWish.Game.PromotionRewardPackConfigSO" preserve="all" />
    <type fullname="GameWish.Game.RangeStayBullet" preserve="all" />
    <type fullname="GameWish.Game.RatePanel" preserve="all" />
    <type fullname="GameWish.Game.RedpoingListener_AdTicketClaim" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Achievement" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_AchievementItem" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ChapterBox" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_CombineSign" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Hero" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_HeroEquip" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_HeroLvlUp" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_HeroLvUp" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_HeroStarUp" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ItemEnoughBtn" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Online" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Passport" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Research" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Shop" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopFreeMixBox" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopItemCraftOrb" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopItemFree" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopItemFreeCoin" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopItemHeroStone" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopItemResFree" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_ShopMixBox10" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Sign" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_SignDayItem" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Sweep" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Task" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_TaskItem" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_TaskLivenessItem" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_Treasure" preserve="all" />
    <type fullname="GameWish.Game.RedpointListener_TreasureItem" preserve="all" />
    <type fullname="GameWish.Game.ReputationPanel" preserve="all" />
    <type fullname="GameWish.Game.ReputationPanel_Item" preserve="all" />
    <type fullname="GameWish.Game.ReputationPanel_ResrouceItem" preserve="all" />
    <type fullname="GameWish.Game.ResearchPanel" preserve="all" />
    <type fullname="GameWish.Game.ResearchPanelBonusItem" preserve="all" />
    <type fullname="GameWish.Game.ResearchPanelLineSlider" preserve="all" />
    <type fullname="GameWish.Game.ResearchPanelRowGroup" preserve="all" />
    <type fullname="GameWish.Game.ResearchPanelUpgradeItem" preserve="all" />
    <type fullname="GameWish.Game.ResearchUpgradePanel" preserve="all" />
    <type fullname="GameWish.Game.ReviveBar" preserve="all" />
    <type fullname="GameWish.Game.RewardAgainPanel" preserve="all" />
    <type fullname="GameWish.Game.RewardDetailItem" preserve="all" />
    <type fullname="GameWish.Game.RewardDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.RewardPanel" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCDGroup" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCDItem" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_FireArrow" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_FireBreath" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_GuardBlade" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_Guillotine" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_HammerBounce" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_HolySword" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_IceArrow" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_IceStorm" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_LightningPulse" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_Plant" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillCtrller_SkySword" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillFuncConfigSO" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillIconItem" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillListItem" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillListPanel" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillSelectItem" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillSelectPanel" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillUpgradePanel" preserve="all" />
    <type fullname="GameWish.Game.RogueSkillUpgradePropItem" preserve="all" />
    <type fullname="GameWish.Game.RoleAnimancer" preserve="all" />
    <type fullname="GameWish.Game.RoleAnimSO" preserve="all" />
    <type fullname="GameWish.Game.SettingPanel" preserve="all" />
    <type fullname="GameWish.Game.SettingToggleButton" preserve="all" />
    <type fullname="GameWish.Game.ShopGiftBagItem" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_ADTicketDaily" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_EquipMarketRefresh" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_Equipment" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_EquipmentAdv" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_ExchangeCoin" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_ExchangeCraftOrb" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_ExchangeHeroStone" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_GiftPack" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_GiftPackHeroCtrl" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_MarketItem" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_MixDrawBox" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_Purchase" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_PurchaseGem" preserve="all" />
    <type fullname="GameWish.Game.ShopItem_RemoveAds" preserve="all" />
    <type fullname="GameWish.Game.ShopItemHideWatcher" preserve="all" />
    <type fullname="GameWish.Game.ShopPanel" preserve="all" />
    <type fullname="GameWish.Game.ShopRewardItem" preserve="all" />
    <type fullname="GameWish.Game.ShopTabRoot" preserve="all" />
    <type fullname="GameWish.Game.SiegeEnemyCtrller" preserve="all" />
    <type fullname="GameWish.Game.SignPanel" preserve="all" />
    <type fullname="GameWish.Game.SignPanelItem" preserve="all" />
    <type fullname="GameWish.Game.SkillBaseCtrller" preserve="all" />
    <type fullname="GameWish.Game.SkillIcon" preserve="all" />
    <type fullname="GameWish.Game.SkillInfoDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.SoldierArcherCtrller" preserve="all" />
    <type fullname="GameWish.Game.SoldierBerserkerCtrller" preserve="all" />
    <type fullname="GameWish.Game.SoldierCtrller" preserve="all" />
    <type fullname="GameWish.Game.SoldierGuardianCtrller" preserve="all" />
    <type fullname="GameWish.Game.SoldierMageCtrller" preserve="all" />
    <type fullname="GameWish.Game.SoldierWarriorCtrller" preserve="all" />
    <type fullname="GameWish.Game.SupplyPowerPanel" preserve="all" />
    <type fullname="GameWish.Game.SweepPanel" preserve="all" />
    <type fullname="GameWish.Game.TaskGroup" preserve="all" />
    <type fullname="GameWish.Game.TaskMainPanel" preserve="all" />
    <type fullname="GameWish.Game.TaskPanel" preserve="all" />
    <type fullname="GameWish.Game.TaskPanelArchieveItem" preserve="all" />
    <type fullname="GameWish.Game.TaskPanelTaskItem" preserve="all" />
    <type fullname="GameWish.Game.ThunderCloudBullet" preserve="all" />
    <type fullname="GameWish.Game.ToggleTitleButton" preserve="all" />
    <type fullname="GameWish.Game.TopBlockPanel" preserve="all" />
    <type fullname="GameWish.Game.TornadoBullet" preserve="all" />
    <type fullname="GameWish.Game.TrackingMoveBullet" preserve="all" />
    <type fullname="GameWish.Game.TreasureDetailPanel" preserve="all" />
    <type fullname="GameWish.Game.TreasurePanel" preserve="all" />
    <type fullname="GameWish.Game.TreasureSelectItem" preserve="all" />
    <type fullname="GameWish.Game.TreasureSelectPanel" preserve="all" />
    <type fullname="GameWish.Game.TreasureSlotItem" preserve="all" />
    <type fullname="GameWish.Game.UIAdticketIcon" preserve="all" />
    <type fullname="GameWish.Game.WallFallBullet" preserve="all" />
    <type fullname="GameWish.Game.WeaponShowCtrller" preserve="all" />
    <type fullname="GameWish.Game.WeaponShowCtrller_ArrowTower" preserve="all" />
    <type fullname="GameWish.Game.WeaponShowCtrller_Throw" preserve="all" />
    <type fullname="GameWish.Game.WoodDropBoxCtrller" preserve="all" />
    <type fullname="GameWish.Game.WorldNormalUIPanel" preserve="all" />
    <type fullname="GameWish.Game.WorldTopUIPanel" preserve="all" />
    <type fullname="Pathfinding.SeikamiGraphUpdateScene" preserve="all" />
    <type fullname="Qarth.AutoTranslation" preserve="all" />
    <type fullname="Qarth.ButtonSoundCompo" preserve="all" />
    <type fullname="Qarth.CircleMaskPanel" preserve="all" />
    <type fullname="Qarth.ColorFadeTransition" preserve="all" />
    <type fullname="Qarth.ElementUIAnimCustomStart" preserve="all" />
    <type fullname="Qarth.FloatMessageItem" preserve="all" />
    <type fullname="Qarth.FloatMessagePanel" preserve="all" />
    <type fullname="Qarth.GuideHandPanel" preserve="all" />
    <type fullname="Qarth.I18N.LocalizeStringEvent" preserve="all" />
    <type fullname="Qarth.MyMaskPanel" preserve="all" />
    <type fullname="Qarth.OfficialVersionAdPanel" preserve="all" />
    <type fullname="Qarth.PopButton" preserve="all" />
    <type fullname="Qarth.RotateForever" preserve="all" />
    <type fullname="Qarth.SortingOrderObserver" preserve="all" />
    <type fullname="Qarth.SoundButton" preserve="all" />
    <type fullname="Qarth.SpritesData" preserve="all" />
    <type fullname="Qarth.ToggleExtend" preserve="all" />
    <type fullname="Qarth.USimpleListView" preserve="all" />
    <type fullname="SuperScrollView.LoopListView2" preserve="all" />
    <type fullname="UITextAutoLine" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.NonDrawingGraphic" preserve="all" />
    <type fullname="UnityEngine.UI.Extensions.TextPic" preserve="all" />
    <type fullname="UVscroll" preserve="all" />
    <type fullname="DoozyUI.Anim" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Fade" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.FadeLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Loop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Move" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.MoveLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Rotate" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.RotateLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Scale" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.ScaleLoop" preserve="nothing" serialized="true" />
    <type fullname="Qarth.SerializeFieldData" preserve="nothing" serialized="true" />
    <type fullname="GameWish.Game.AnimStateConf" preserve="nothing" serialized="true" />
    <type fullname="Qarth.I18N.UnityEventText" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Extensions.TextPic/HrefClickEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Extensions.TextPic/IconName" preserve="nothing" serialized="true" />
    <type fullname="SuperScrollView.ItemPrefabConfData" preserve="nothing" serialized="true" />
    <type fullname="GameWish.Game.PromotionRewardConfig" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="AstarPathfindingProject, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Pathfinding.FunnelModifier" preserve="all" />
    <type fullname="Pathfinding.RaycastModifier" preserve="all" />
    <type fullname="Pathfinding.Seeker" preserve="all" />
    <type fullname="Pathfinding.PathfindingTag" preserve="nothing" serialized="true" />
    <type fullname="Pathfinding.GraphMask" preserve="nothing" serialized="true" />
    <type fullname="Pathfinding.StartEndModifier" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Aya.DataBinding, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Aya.DataBinding.TextBinder" preserve="all" />
    <type fullname="Aya.DataBinding.TextFormatValueBinder" preserve="all" />
  </assembly>
  <assembly fullname="Coffee.UIParticle, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIExtensions.UIParticle" preserve="all" />
  </assembly>
  <assembly fullname="EasyWallCollider, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="PepijnWillekens.EasyWallColliderUnity.EasyWallCollider" preserve="all" />
  </assembly>
  <assembly fullname="Game.Rendering.GPUAnimation.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="GameWish.Game.GPUSkinningAnimation" preserve="all" />
    <type fullname="GameWish.Game.GPUSkinningBone" preserve="nothing" serialized="true" />
    <type fullname="GameWish.Game.GPUSkinningClip" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Game.Rendering.Pipeline.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Game.Core.Rendering.AfterImageController" preserve="all" />
  </assembly>
  <assembly fullname="Game.Rendering.UIEffect.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Game.Core.UIEffect.GraphicGroup" preserve="all" />
    <type fullname="Game.Core.UIEffect.UIEffect" preserve="all" />
    <type fullname="Game.Core.UIEffect.UIOutline" preserve="nothing" serialized="true" />
    <type fullname="Game.Core.UIEffect.UIGradient" preserve="nothing" serialized="true" />
    <type fullname="Game.Core.UIEffect.UIGrey" preserve="nothing" serialized="true" />
    <type fullname="Game.Core.UIEffect.UIShiny" preserve="nothing" serialized="true" />
    <type fullname="Game.Core.UIEffect.UIFill" preserve="nothing" serialized="true" />
    <type fullname="Game.Core.UIEffect.UIFadeLoop" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Game.Timeline.GenerateObject, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Game.Timeline.EnemySpawnClipAsset" preserve="all" />
    <type fullname="Game.Timeline.EnemySpawnTrackAsset" preserve="all" />
    <type fullname="Game.Timeline.GenerateSetting" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Game.UI.Fundation.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Game.UI.Foundation.DesignImage" preserve="all" />
    <type fullname="LongPressButton" preserve="all" />
  </assembly>
  <assembly fullname="MeshPaint.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Game.Tool.MeshPaint" preserve="all" />
    <type fullname="Game.Tool.MeshPaint/Detail" preserve="nothing" serialized="true" />
    <type fullname="Game.Tool.UndoManager" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="MoreMountains.Tools, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="MoreMountains.Feedbacks.MMFlash" preserve="all" />
    <type fullname="MoreMountains.Feedbacks.MMFlashDebugSettings" preserve="nothing" serialized="true" />
    <type fullname="MoreMountains.Tools.MMTweenType" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="spine-unity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Spine.Unity.BoneFollowerGraphic" preserve="all" />
    <type fullname="Spine.Unity.SkeletonDataAsset" preserve="all" />
    <type fullname="Spine.Unity.SkeletonGraphic" preserve="all" />
    <type fullname="Spine.Unity.SkeletonSubmeshGraphic" preserve="all" />
    <type fullname="Spine.Unity.SpineAtlasAsset" preserve="all" />
    <type fullname="Spine.Unity.MeshGenerator" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator/Settings" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.BlendModeMaterials" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.BlendModeMaterials/ReplacementMaterial" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.Universal.DecalProjector" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalLightData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.TemporalAA/Settings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.TMP_SpriteAsset" preserve="all" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Timeline, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Timeline.TimelineAsset" preserve="all" />
    <type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Timeline.TimelineClip" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animation" preserve="all" />
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.AvatarMask" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioClip" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightmapSettings" preserve="all" />
    <type fullname="UnityEngine.LineRenderer" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.RenderSettings" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.TrailRenderer" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Boolean]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.Dropdown" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.InputField" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Outline" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/EndEditEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="Animancer">
    <type fullname="Animancer.AnimancerEvent/Sequence/Serializable" preserve="nothing" serialized="true" />
    <type fullname="Animancer.ClipTransition" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sirenix.Serialization">
    <type fullname="Sirenix.Serialization.SerializationData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule">
    <type fullname="UnityEditor.AnimatedValues.AnimBool" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>