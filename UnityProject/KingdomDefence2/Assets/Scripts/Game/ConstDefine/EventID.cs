using UnityEngine;
using System.Collections;
using Qarth;

namespace GameWish.Game
{
    public enum EventID
    {
        None,

        OnJoystickMove,

        OnLoadPhaseUpdated,
        OnHideLogoPanel,
        OnLoadGameRes,
        OnLoadWorldDetail,

        OnGuideFuncUnlocked,

        OnOpenPrivacy,

        OpenMap,
        OpenTask,
        OpenBottomBarFunc,

        //game
        OnCloseIDPanel,
        Back2Stage,
        ResetBottom2Stage,

        OnUpdateSubscribe,
        OnBoostStateChange,
        OnTableReloaded,
        OnWorldLoaded,
        OnUpdateGameCoin,
        OnUpdateGameRp,

        OnChestRewardOver,
        OnCraftOrbAdd,
        OnBuildingKeyAdd,
        OnHeroUpgradeStoneAdd,
        OnTreasureAdd,
        OnWoodAdd,
        OnCoinAdd,
        OnGemAdd,
        OnMudAdd,
        OnExpAdd,
        OnExpAddAnim,
        OnLevelAdd,
        OnLevelAddAnim,
        OnLevelUpRewards,
        OnNewDay,


        //shop
        OnClickExchangeItemHint,
        OnGiftBagPurchased,
        OnShopPackItemPurchased,
        OnDrawOver,


        // bonus
        OnDailyBonusUpdate,
        OnDailyBonusGet,
        OnDailyBonusTick,
        OnAdCounted,
        OnAdRewarded,

        OnDropCoinBonusOpenByUI,
        OnDropCoinBonusUpdate,
        OnDropCoinTaken,
        OnDropGemBoxTaken,
        OnDropBoxTaken,

        //ui
        OnPanelClose,
        OnCallBottomPanel,

        OnSceneLoaded,
        OnStartLoading,
        OnEndLoading,
        OnShopRewardOver,

        OnFreqAdLocked,
        OnUpdateShopFree,

        OnClickResearchItem,
        OnResearchItemReloaded,
        OnResearchItemSet,

        // task
        OnTaskHandled,
        OnTaskRewarded,
        OnTaskCanReward,
        OnLivenessRewarded,
        OnLivenessComplete,

        //sign
        CheckSignable,
        OnSignReset,
        OnDoSign,
        OnDoSignAgain,
        OnSignDayRewarded,

        //building
        // OnBuildingCanUnlock,
        OnBuildingUnlock,
        RequestBuildingUpgrade,

        RequestBuildingRevert,
        OnBuildingUpgradeStar,
        OnBuildingUpgradeLevelGlobal,
        OnBuildingUpgradeLevel,
        OnBuildingModelChanged,
        OnBuildingActivate,
        OnBuildingBuildSelect,
        OnBuildingBroken,
        OnBuildingUpgradeIdSet,

        OnBuildingRevert,
        OnDefenceWallBroken, //当城墙被摧毁时
        OnBuildingCostRefresh,

        OnRequestCameraFollow,
        OnUpdateBuildingMoney,

        OnBattleBuildingSelectBuff,

        //upgradeInfo
        OnUpgradeInfo,
        OnAddedUpgradeItem,
        OnPackItemStack,
        OnPackItemAdd,
        OnPackMaxAdd,


        //role
        OnRoleChangeState,


        //research
        OnResearchItemLvlUp,
        OnResearchUnlock,
        
        //altar
        OnAltarSummonHero,


        //Guide
        OnFirstPlayGame,
        OnCheckSceneGuideStepFinish,
        OnSetHighlightUI,
        OnSetGuideButton,
        OnClickGuideBtn,

        OnGuideCommandStart,
        OnGuideCommandEnd,
        OnGuideTowardsNPC,
        OnGuideTowardsBuilding,
        OnGuideMgrSettle,

        //click
        OnTapOnBuilding,


        //mission
        DoEvtUnlockBuilding,
        DoProgressUnlockBuilding,
        RequestBuildingUnlock,

        OnSummonHero,
        OnSummonHeroComplete,

        //prommotion
        OnPromotionUITrigger,
        OnPromotionAdTrigger,

        //battle
        OnMove,
        OnAttackActionTrigger,
        OnHitDmg,
        OnReceiveDmg,
        OnDmgHitHandled,
        OnReceiveHitFinalDmg,
        OnBuffAdd,
        OnShieldAdd,
        OnShieldBroken,
        OnReceiveBuff,
        OnHitBackRole,

        OnClearDebuffAll,
        OnCleanPoisonArea,

        OnRoleBorn,
        OnRoleDesireAttack,
        OnRoleDesireSkill,
        OnRoleAttackOver,
        OnSetRoleAITarget,

        OnEntityDie,
        OnEntityHpZero,
        OnEntityHpUpdate,
        OnKillEntity,
        OnEntityBattleRTDataRefresh,
        OnEntityStunned,
        OnEntityRevived,
        OnEntityPlayAtkAnim,
        OnEntityShowHitEffect,
        OnEntityDodge,
        OnEntityBlocked,
        OnBossHpUpdate,
        OnEnemyHitToleranceClear,
        OnHitToleranceReset,

        OnMainHeroChanged,
        OnAllHeroDead,
        OnSkillRevived,
        OnMainHeroRevived,

        OnSummonItemClicked,
        OnSummonedHeroCleanUpdate,
        OnSummonRefreshTimeUpdate,
        OnSummonRefreshTimeOver,

        OnHeroListItemClicked,
        OnHeroMainSet,
        OnHeroTeammateSet,
        OnHeroUpgrade,
        OnHeroGet,
        OnHeroGetTips,
        OnUpdateHeroUnlockTrigger,
        OnHeroAddPiece,

        OnRideChange,
        OnFlyAnimEnd,

        OnSkillReady,
        OnSkillAllocated,
        OnSkillFuncFinished,
        OnSkillStartAction,
        OnSkillActionOver,
        OnSkillCacheTargets,
        OnSkillActionTrigger,

        //globalskill
        OnGlobalSkillRefresh,
        OnGlobalSkillSelectFull,
        OnGlobalSkillShowDuration,
        OnGlobalSkillSelect,
        
        //rogueskill
        OnRogueSkillSelect,
        OnRogueSkillFuncSelect,
        OnRogueSkillUpgrade,
        OnRogueSkillPieceUpdate,
        OnRogueSkillCast,
        
        OnRogueSkillSelectRefresh,
        UpdateRogueSkillState,

        OnBarracksUpgrade,

        OnChapterLoaded,
        OnChapterStart,
        OnBattleRestart,
        OnBattleBuffAdd,
        OnBattleWaveStart,
        OnBattleWaveEnd,
        OnBattleBossShow,
        OnBattleLevelEnd,
        OnRecordMaxWave,
        OnBattleBuffOver,
        OnBattleBuffCounted,
        OnBattleWavePass,

        OnBattleStartHide,

        OnExitNight,

        OnSoldierDied,
        OnSolderModelLoaded,
        OnHeroHit,

        OnKillBoss,
        OnBossDie,
        OnEnemyDie,

        OnEnemyDropWood,
        OnDropBag,
        OnPlayShakeSmall,
        OnMainHallBroken,

        OnTowerBroken,

        //Dungeon
        OnDungeonTeamUpdate,
        OnDungeonEnemiesClear,
        OnDungeonCrystalBroken,

        // Equipment
        OnEquipmentGet,
        OnEquipmentEquip,
        OnEquipmentUnEquip,
        OnEquipmentMerge,
        OnEquipmentSlotUpgrade,
        OnEquipmentUpgradeQuality,
        OnEquipmentBoxOpen,

        // Treasure
        OnTreasureEquip,
        OnTreasureBuy,
        OnReceiveTreasure,
        OnReceiveTreasurePiece,
        OnReceivePlayerTreasure,
        OnShowTreasure,
        OnTreasureUpgrade,

        OnOfflineSweep,
        OnUpdateOnlineRedpoint,

        // PlayerInfo
        OnPlayerInfoUpdate,
        OnPlayerLevelUp,
        OnUpdatePlayerPower,

        PlayAnimation,

        //shop_Equipment
        OnRefreshEquipMarketItem,
        OnUpdateRefreshEquipMarketNum,
        OnBuyEquipMarketItem,

        //heroslot
        OnUnlockHeroSlotItem,
        OnUnlockSlotPurchased,

        //chapter
        OnMainHeroDie,
        OnMainHallHurt,
        OnGetChapterBoxReward,
        OnPassChapter,
        OnCompleteChapter,
        OnEnterChapterPanel,

        // BattleBuff
        OnBattleBuffRefresh,
        OnClickBuildingBattleBuffItem,

        //heroequipPanel
        OnSelectHero,
        OnOpenHeroLvUpPanel,

        //online
        OnPassOnlineTime,
        OnGetOnlineReward,

        //passport
        OnGetPassReward,

        //sweep
        OnUpdateSweepState,

        //Gather
        OnGather,
        OnGatherSelected,

        //adticket
        OnUpdateShopAdTicketsActive,
        OnGetShopAdTicketsReward,

        //freegift
        OnStartDoubleCoin,
        OnStartFreeHero,

        OnReceiveBuildingPiece,

        //
        ShowBottomBar,
        HideBottomBar,
    }
}