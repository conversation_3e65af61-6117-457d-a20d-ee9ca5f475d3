using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Qarth;
using System;
using UnityEngine.UI;

namespace GameWish.Game
{
    [TMonoSingletonAttribute("[Game]/GameMgr")]
    public class GameMgr : AbstractModuleMgr, ISingleton
    {
        private static GameMgr s_Instance;
        private int m_GameplayInitSchedule = 0;

        private float m_DeviceRealWidth;

        public float DeviceRealWidth
        {
            get
            {
                if (m_DeviceRealWidth == 0)
                {
                    m_DeviceRealWidth = UIMgr.S.uiRoot.rootCanvas.rectTransform().rect.width;
                }

                return m_DeviceRealWidth;
            }
        }

        public static GameMgr S
        {
            get
            {
                if (s_Instance == null)
                {
                    s_Instance = MonoSingleton.CreateMonoSingleton<GameMgr>();
                }

                return s_Instance;
            }
        }

        public void InitGameMgr()
        {
            Log.i("Init[GameMgr]");
            // AppropriateLongScreen();
            // StartCoroutine(AppropriateLongScreen());
        }

        public void OnSingletonInit()
        {
        }

        protected override void OnActorAwake()
        {
            ShowLogoPanel();
            EventSystem.S.Register(SDKEventID.OnPemissionCallBack, OnPermissionCallBack);
        }

        protected override void OnActorStart()
        {
            StartProcessModule module = AddMonoCom<StartProcessModule>();
            module.SetFinishListener(OnStartProcessFinish);
        }

        protected void ShowLogoPanel()
        {
            UIDataModule.RegisterStaticPanel();

            Action a = OnLogoPanelFinish;
            UIMgr.S.OpenTopPanel(UIID.LogoPanel, null, a);
        }

        protected void OnLogoPanelFinish()
        {
            ++m_GameplayInitSchedule;
            TryStartGameplay();
        }

        protected void OnStartProcessFinish()
        {
            ++m_GameplayInitSchedule;
            TryStartGameplay();
        }

        protected void TryStartGameplay()
        {
            if (m_GameplayInitSchedule < 2)
            {
                return;
            }

            GameSaveMgr.S.Init(() =>
            {
                SDKMgr.S.InitAds();
                GameplayMgr.S.InitGameplay();
            });
        }

        public void StartGuide()
        {
            if (PlayerPrefs.GetInt("IsGuide", 0) == 0)
            {
                GetCom<GuideModule>().StartGuide();
            }
        }

        void OnPermissionCallBack(int key, params object[] args)
        {
        }

        private void OnApplicationFocus(bool focusStatus)
        {
            if (!focusStatus)
            {
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
            }
        }

        private void OnApplicationQuit()
        {
        }

        IEnumerator AppropriateLongScreen()
        {
            yield return new WaitForSeconds(1);
            var scaler = UIMgr.S.uiRoot.rootCanvas.GetComponent<CanvasScaler>();
            var rectRoot = UIMgr.S.uiRoot.panelRoot.GetComponent<RectTransform>();
            float offsetMaxY = Screen.height - Screen.safeArea.height - Screen.safeArea.yMin;
            // if (offsetMaxY < 10)
            // {
            //     offsetMaxY = Define.LONG_SCREEN_OFFSET_TOP;
            // }
            rectRoot.offsetMax = new Vector2(rectRoot.offsetMax.x, -offsetMaxY);
            rectRoot.offsetMin = new Vector2(rectRoot.offsetMin.x, Screen.safeArea.yMin);
            scaler.referenceResolution = new Vector2(scaler.referenceResolution.x, scaler.referenceResolution.y + offsetMaxY + Screen.safeArea.yMin);
            m_DeviceRealWidth = scaler.referenceResolution.y / Screen.height * Screen.width;
            ++m_GameplayInitSchedule;
            TryStartGameplay();
        }
    }
}