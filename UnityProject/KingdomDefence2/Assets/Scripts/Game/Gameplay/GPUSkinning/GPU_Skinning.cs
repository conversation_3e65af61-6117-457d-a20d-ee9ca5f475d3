using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using PrimeTween;
using Sirenix.OdinInspector;

namespace GameWish.Game.AnimInstancing
{
    [ExecuteAlways]
    public class GPU_Skinning : GPUSkinningPlayerMono
    {
        private Dictionary<string, List<GPU_AnimationEvent>> m_DictAnimationEvts =
            new Dictionary<string, List<GPU_AnimationEvent>>();

        private GPU_AnimationTime m_GPU_Animation1Time;

        private Tween m_GlintTweener;
        private float m_GlintTimer = GlintTimer;
        private const float GlintTimer = 0.2f;
        private static readonly int ShaderID_Glint = Shader.PropertyToID("_Glint");
        private static float _GlintValue = 0.5f;

        private static readonly int ShaderID_GreyIntensity = Shader.PropertyToID("_GreyIntensity");
        private Tween m_GreyTweener;


        private bool m_IsDissolve = false;
        private static readonly int ShaderID_Dissolve = Shader.PropertyToID("_DissolveThreshold");

        protected override void Init()
        {
            base.Init();
            m_GPU_Animation1Time = new GPU_AnimationTime();
        }

        public void Reset()
        {
            m_IsDissolve = false;
            player.materialPropertyBlock.SetFloat(ShaderID_Dissolve, 0);
            player.materialPropertyBlock.SetFloat(ShaderID_GreyIntensity, 0);
            player.MeshRenderer.SetPropertyBlock(player.materialPropertyBlock);
        }

        public void PlayGlintAnimation()
        {
            if (m_GlintTimer > 0)
                return;
            m_GlintTimer = GlintTimer;

            if (m_GlintTweener.IsPlaying())
            {
                m_GlintTweener.Kill();
            }

            player.materialPropertyBlock.SetFloat(ShaderID_Glint, _GlintValue);
            m_GlintTweener = Tween
                .Custom(_GlintValue, 0, 0.15f, (value) =>
                {
                    player.materialPropertyBlock.SetFloat(ShaderID_Glint, value);
                    player.MeshRenderer.SetPropertyBlock(player.materialPropertyBlock);
                })
                .SetEase(Ease.InQuint);
        }

        public void PlayGrey(float duration)
        {
            if (m_GreyTweener.IsPlaying())
            {
                m_GreyTweener.Kill();
            }

            player.materialPropertyBlock.SetFloat(ShaderID_GreyIntensity, 0);
            m_GreyTweener = Tween
                .Custom(0, 1, duration, (value) =>
                {
                    player.materialPropertyBlock.SetFloat(ShaderID_GreyIntensity, 1);
                    player.MeshRenderer.SetPropertyBlock(player.materialPropertyBlock);
                })
                .SetEase(Ease.InQuint);
        }


        public void PlayDissolveAnimation(float duration, Action complete, float delay = 1)
        {
            if (m_IsDissolve)
                return;

            m_IsDissolve = true;

            player.materialPropertyBlock.SetFloat(ShaderID_Dissolve, 0);
            Tween.Custom(0, 1, duration, (value) =>
                {
                    player.materialPropertyBlock.SetFloat(ShaderID_Dissolve, value);
                    player.MeshRenderer.SetPropertyBlock(player.materialPropertyBlock);
                })
                .SetDelay(delay)
                .SetEase(Ease.InQuint)
                .OnComplete(() => complete?.Invoke());
        }

        public GPUSkinningClip GetClip(string animName)
        {
            return anim.GetGPUSkinningClip(animName);
        }

        public float GetAnimClipLength(string animName)
        {
            var clip = anim.GetGPUSkinningClip(animName);
            if (clip != null)
                return clip.length;
            return 0f;
        }

        public void AddAnimEvt(string animName, float length, System.Action evtCallback)
        {
            if (!player.IsAnimSupported(animName))
                return;

            if (!m_DictAnimationEvts.ContainsKey(animName))
                m_DictAnimationEvts.Add(animName, new List<GPU_AnimationEvent>());

            float targetLength = 0;
            for (int i = 0; i < anim.clips.Length; i++)
            {
                if (animName == anim.clips[i].name)
                {
                    targetLength = anim.clips[i].length;
                    break;
                }
            }

            if (targetLength == 0)
            {
                Debug.LogError("anim not found, lengh is zero");
                return;
            }

            if (length > 1 || length < 0)
                return;

            //这里的length实际是normalizeTime
            m_DictAnimationEvts[animName].Add(new GPU_AnimationEvent(length, evtCallback));
        }


        protected override void Update()
        {
            if (player == null)
                return;

            player.Update(Time.deltaTime * GameTimeMgr.S.globalTS);
            m_GPU_Animation1Time.Update(Time.deltaTime * m_Speed * GameTimeMgr.S.globalTS);

            m_GlintTimer -= Time.deltaTime * GameTimeMgr.S.globalTS;
        }


        public void PlayAnimation(string animationName, System.Action endAction = null, float lerpTime = 0.2f)
        {
            if (!IsAnimSupported(animationName))
            {
                return;
            }

            if (lerpTime > 0)
                player.CrossFade(animationName, lerpTime, endAction);
            else
                player.Play(animationName, endAction);

            m_GPU_Animation1Time.Init(player.playingClip.length,
                player.playingClip.wrapMode == GPUSkinningWrapMode.Loop);
            m_GPU_Animation1Time.SetProgress(0);

            // 检查动画中事件
            if (m_DictAnimationEvts.ContainsKey(animationName) &&
                m_DictAnimationEvts[animationName].Count > 0)
            {
                for (int i = 0; i < m_DictAnimationEvts[animationName].Count; i++)
                {
                    m_GPU_Animation1Time.InsertCustomEvt(m_DictAnimationEvts[animationName][i]);
                }

                m_GPU_Animation1Time.SetEventActive(true);
            }
        }

        public void SetSpeed(float speed)
        {
            m_Speed = speed;
            player.SetSpeed(m_Speed);
        }

        public bool IsAnimSupported(string animationName)
        {
            return player.IsAnimSupported(animationName);
        }


#if UNITY_EDITOR
        void OnEnable()
        {
            //非运行情况下，预览使用
            ResetAnim();
        }

        [Button]
        void ResetAnim()
        {
            if (Application.isPlaying)
                return;

            // if (player == null)
            Init();
            player.Update(Time.deltaTime * GameTimeMgr.S.globalTS);
        }

#endif
    }
}