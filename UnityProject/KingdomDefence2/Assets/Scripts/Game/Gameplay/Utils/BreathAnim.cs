using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using PrimeTween;
using Qarth;


namespace GameWish.Game
{
	public class BreathAnim : MonoBehaviour
	{
		public Vector3 initScale = Vector3.one;
		public float duration = 0.5f;
		public int loop = -1;
		private Sequence m_Sequence;
		void OnEnable()
		{
			transform.DOKill();
			transform.localScale = initScale;
			Tween.Scale(transform, Vector3.one * 1.1f, duration, Ease.Linear, loop, CycleMode.Yoyo);
		}

		void OnDisable()
		{
			transform.DOKill();
			transform.localScale = initScale;
		}
	}

}