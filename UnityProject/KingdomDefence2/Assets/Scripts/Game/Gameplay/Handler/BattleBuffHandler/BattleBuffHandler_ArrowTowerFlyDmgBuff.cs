using Qarth;
using UnityEngine;

namespace GameWish.Game
{
    public class BattleBuffHandler_ArrowTowerFlyDmgBuff : AbstractBattleBuffHandler
    {
        public float dmgBuffRatio { get; private set; }

        public override void Setup(BattleBuffConfig config)
        {
            if (config is BattleBuffConfig_ArrowTowerFlyDmgBuff _Config)
            {
                dmgBuffRatio = _Config.dmgBuffRatio;
            }
        }

        protected override bool IsSuitable(EntityMonoBase entity, params object[] args)
        {
            if (entity is ArrowTowerCtrller _building && args.Length > 0 && args[0] is BuildingFunction_RangeAttack)
                return _building.DefinitionType == EntityDefinitionType.ArrowTower;

            return false;
        }

        protected override void DoFunctionLogic(EntityMonoBase entity, params object[] args)
        {
            var bulletHolder = (BulletConfHolder)args[1];
            bulletHolder.dmgFlyBuffRatio += dmgBuffRatio;
        }

        public override void DoClean()
        {
            ObjectPool<BattleBuffHandler_ArrowTowerFlyDmgBuff>.S.Recycle(this);
        }
    }
}