using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GameWish.Game
{
    public class UpgradeHeroLevelTaskHandler : UpgradeHeroTaskHandler
    {
        private int m_TargetLevel;

        public override void LoadGoal(int goalInfo)
        {
            if (m_Recorder != null)
                m_Recorder.SetGoal(1);
            m_TargetLevel = goalInfo;
        }

        protected override void TriggerTaskProgress(params object[] args)
        {
            if (args != null && args.Length > 0)
            {
                int id = (int)args[0];
                var heroData = WorldInfoMgr.data.heroData.GetHeroData(id);
                //判断所有英雄有没有达到m_TargetLevel的
                if (heroData.star >= m_TargetLevel)
                    m_Recorder.AddCurrent(1);
            }
        }
    }
}
