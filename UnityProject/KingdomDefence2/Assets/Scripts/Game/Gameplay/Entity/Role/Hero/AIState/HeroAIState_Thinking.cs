using System.Collections.Generic;
using UnityEngine;
using Qarth;
using Animancer;
using PrimeTween;

namespace GameWish.Game
{
    public class HeroAIState_Thinking : RoleAIState
    {
        public enum SkillStateEnum
        {
            None,
            Ready,
            Casting,
        }

        protected HeroAIComponent m_HeroAI;
        protected bool m_CanAction;
        protected float m_TargetPickTimer;
        private SkillStateEnum m_SkillState;
        protected float m_TargetRadius;

        private int m_DeltaDir;
        private float m_ChaseRatio = 0.8f;


        public HeroAIState_Thinking(RoleAIComponent owner, RoleAIComponent.AIStateEnum stateEnum)
            : base(owner, stateEnum)
        {
            m_HeroAI = owner as HeroAIComponent;
        }

        public override void Enter(RoleBaseCtrller mgr)
        {
            base.Enter(mgr);
            m_TargetPickTimer = 0.5f;
            m_CanAction = true;
            mgr.RegisterSelfEvt(EventID.OnSkillReady, OnSkillReady);
            mgr.RegisterSelfEvt(EventID.OnSkillAllocated, OnSkillAllocate);
            mgr.RegisterSelfEvt(EventID.OnSkillActionOver, OnSkillActionOver);
        }

        public override void Execute(RoleBaseCtrller mgr, float dt)
        {
            base.Execute(mgr, dt);

            if (!m_CanAction || mgr == null || mgr.runtimeData == null)
                return;

            if (m_TargetPickTimer > 0)
            {
                m_TargetPickTimer -= dt;
            }

            if (m_AIComponent.aiTarget == null)
            {
                if (m_TargetPickTimer <= 0)
                {
                    m_TargetPickTimer = 0.5f;
                    FindNewAITarget(mgr);

                    if (m_SkillState != SkillStateEnum.Casting)
                    {
                        if (HeroTeamMgr.S.mainHero == mgr)
                            m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Idle);
                        else
                        {
                            if (m_AIComponent.aiTarget != null)
                            {
                                m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Chasing);
                            }
                            else
                            {
                                m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Wander);
                            }
                        }
                    }
                }
            }
            else
            {
                if (m_SkillState == SkillStateEnum.Casting)
                    return;
                
                // 这个做法会排除掉复活技能目标，后期需特殊处理
                if (m_AIComponent.aiTarget.GetEntityBattleRTData() != null &&
                    m_AIComponent.aiTarget.GetEntityBattleRTData().currentHp <= 0 &&
                    !SkillMgr.S.IsReviveSkill(mgr.runtimeData.curReadySkill))
                {
                    FindNewAITarget(mgr);
                }
                else
                {
                    if (BattleGroundMgr.S.battleState == BattleStateEnum.Battle)
                    {
                        m_TargetRadius = m_AIComponent.aiTarget.GetEntityBattleRTData() == null
                            ? 0.5f
                            : m_AIComponent.aiTarget.GetEntityBattleRTData().bodyRadius / 2f;
                        
                        if (m_SkillState == SkillStateEnum.Ready && mgr.runtimeData.curReadySkill != null)
                        {
                            var tars = SkillMgr.S.GetAffectTargets(mgr, mgr.runtimeData.curReadySkill.mainFunc);
                            if (tars.Count > 0)
                            {
                                mgr.SendSelfEvt(EventID.OnSkillCacheTargets, tars);
                                m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Skill);
                                m_SkillState = SkillStateEnum.Casting;
                                // Log.e(" skilled");
                            }
                            else
                            {
                                if (HeroTeamMgr.S.mainHero != mgr)
                                {
                                    m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Chasing);
                                }
                            }
                        }
                        else if (m_SkillState == SkillStateEnum.None)
                        {
                            if (HeroTeamMgr.S.mainHero != mgr)
                            {
                                if (IsOutOfAtkRange(mgr))
                                {
                                    m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Chasing);
                                }
                            }
                        }
                    }
                    else
                    {
                        m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Idle);
                    }
                }
            }
        }

        public override void Exit(RoleBaseCtrller mgr)
        {
            base.Exit(mgr);
        }

        public override void DisposeState()
        {
            m_HeroAI = null;
        }

        bool FindNewAITarget(RoleBaseCtrller mgr)
        {
            if (mgr == null || mgr.runtimeData == null)
                return false;

            m_AIComponent.aiTarget = null;
            m_SkillState = SkillStateEnum.None;

            if (mgr.runtimeData.curReadySkill != null && BattleGroundMgr.S.battleState == BattleStateEnum.Battle)
            {
                m_SkillState = SkillStateEnum.Ready;
                //对主目标的必须配在第一个技能效果
                var mainCheckFunc = mgr.runtimeData.curReadySkill.lstFuncConfs[0];

                var tars = SkillMgr.S.GetAffectTargets(mgr, mainCheckFunc);
                //有目标在技能范围内，可以直接施法
                if (tars.Count > 0)
                {
                    mgr.SendSelfEvt(EventID.OnSkillCacheTargets, tars);
                    m_AIComponent.aiTarget = SkillMgr.S.GetNearestTarget(tars, mgr.GetEntityPosition());
                    m_AIComponent.SetState(RoleAIComponent.AIStateEnum.Skill);

                    // Log.e("skilled");
                    m_SkillState = SkillStateEnum.Casting;
                    return true;
                }

                //没有目标在技能范围内，则获取技能距离范围内所有目标
                tars = SkillMgr.S.GetCanAffectTarget(mgr, mainCheckFunc);
                for (int i = 0; i < tars.Count; i++)
                {
                    if (Vector3.Distance(tars[i].GetEntityPosition(), mgr.GetEntityPosition()) <=
                        SkillMgr.S.GetCastJudgementRange(mainCheckFunc))
                    {
                        m_AIComponent.aiTarget = tars[i];
                        // Log.e("can skill");
                        return false;
                    }
                }
            }

            m_AIComponent.aiTarget = BattleGroundMgr.S.FindNearestEnemyInDistance(
                HeroTeamMgr.S.teamCenterPos,
                mgr.runtimeData.GetRealViewRange());
            return false;
        }

        void OnSkillAllocate(int key, params object[] args)
        {
        }

        void OnSkillReady(int key, params object[] args)
        {
            FindNewAITarget((RoleBaseCtrller) args[0]);
        }

        void OnSkillActionOver(int key, params object[] args)
        {
            m_SkillState = SkillStateEnum.None;
        }

        protected bool IsOutOfAtkRange(RoleBaseCtrller mgr)
        {
            return
                Vector3.Distance(m_AIComponent.aiTarget.GetEntityPosition(), mgr.transform.position) >
                mgr.runtimeData.GetRealAtkRange() + m_TargetRadius;
        }
    }
}