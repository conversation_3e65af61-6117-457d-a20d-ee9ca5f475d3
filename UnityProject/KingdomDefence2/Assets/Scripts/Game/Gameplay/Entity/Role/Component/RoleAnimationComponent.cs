using Animancer;
using UnityEngine;
using System;
using System.Collections.Generic;
using GameWish.Game.AnimInstancing;
using Qarth;

namespace GameWish.Game
{
    public enum AnimStateType
    {
        None,
        Idle,
        Run,
        Walk,
        Attack,
        Hit,
        Die,
        Skill,
        Born,
    }

    public class RoleAnimationComponent : EntityComponent
    {
        protected GPU_Skinning m_GPUAnim;
        private RoleAnimancer m_Animancer;
        private AnimancerState m_CurAnimancerState;

        private Dictionary<string, AnimancerEvent.Sequence> m_DictAnimStateEvts =
            new Dictionary<string, AnimancerEvent.Sequence>();

        private AnimancerLayer m_HighLayer;
        private AnimancerState m_CurHighLayerAnimancerState;

        private Dictionary<string, AnimancerEvent.Sequence> m_DictHighLayerAnimStateEvts =
            new Dictionary<string, AnimancerEvent.Sequence>();

        private Dictionary<string, Action> m_DictHighLayerAnimancerAnimEndActions = new Dictionary<string, Action>();

        public RoleAnimancer animCtrller
        {
            get { return m_Animancer; }
        }

        public RoleAnimationComponent()
        {
        }

        public override void InitComponent(EntityMonoBase owner)
        {
            base.InitComponent(owner);
            if (m_GPUAnim == null)
            {
                m_GPUAnim = owner.GetComponentInChildren<GPU_Skinning>();
                if (m_GPUAnim != null)
                    m_GPUAnim.Reset();
            }

            if (m_Animancer == null)
                m_Animancer = m_Owner.GetComponentInChildren<RoleAnimancer>();

            if (m_Animancer != null)
            {
                m_Animancer.Layers[1].SetMask(m_Animancer.avatarMask);
                m_Animancer.Layers[1].SetDebugName("Action Layer");
                m_HighLayer = m_Animancer.Layers[1];

                m_Animancer.Animator.applyRootMotion = false;
            }


            m_Owner.RegisterSelfEvt(EventID.OnEntityShowHitEffect, ShowHitEffect);
        }

        public override void DoTick(float deltaTime)
        {
        }

        public override void DoClean()
        {
            base.DoClean();

            m_GPUAnim = null;
            m_Animancer = null;
            m_HighLayer = null;
            m_CurAnimancerState = null;
            m_CurHighLayerAnimancerState = null;
            m_DictAnimStateEvts.Clear();
            m_DictHighLayerAnimStateEvts.Clear();
            m_DictHighLayerAnimancerAnimEndActions.Clear();
        }

        public Transform GetAnimTrs()
        {
            if (m_GPUAnim != null)
                return m_GPUAnim.transform;
            if (m_Animancer != null)
                return m_Animancer.transform;
            return null;
        }


        #region play

        public AnimationClip GetAnimationClip(string name)
        {
            if (m_Animancer != null && m_Animancer.animSO != null)
                return m_Animancer.animSO.GetClipByState(name) == null
                    ? null
                    : m_Animancer.animSO.GetClipByState(name).Clip;
            return null;
        }

        public GPUSkinningClip GetGPUSkinningClip(string name)
        {
            if (m_GPUAnim != null)
                return m_GPUAnim.GetClip(name);
            return null;
        }

        public float GetAnimationLength(string name)
        {
            if (m_GPUAnim != null)
                return m_GPUAnim.GetAnimClipLength(name);

            if (m_Animancer != null && m_Animancer.animSO != null)
                return m_Animancer.animSO.GetClipByState(name) == null
                    ? 0f
                    : m_Animancer.animSO.GetClipByState(name).Clip.length;
            return 0f;
        }

        public bool IsAnimSupported(AnimStateType type)
        {
            return IsAnimSupported(type.ToString());
        }

        public bool IsAnimSupported(string animName)
        {
            if (m_GPUAnim != null)
                return m_GPUAnim.IsAnimSupported(animName);
            if (m_Animancer != null)
                return m_Animancer.animSO.GetClipByState(animName) != null;
            return false;
        }

        public bool IsGPUAnimation()
        {
            return m_GPUAnim != null;
        }

        public bool IsAnimLooped(string animName)
        {
            if (!IsAnimSupported(animName))
                return false;

            if (m_GPUAnim != null)
                return m_GPUAnim.GetClip(animName).wrapMode == GPUSkinningWrapMode.Loop;
            if (m_Animancer != null)
                return m_Animancer.animSO.GetClipByState(animName).IsLooping;
            return false;
        }

        public void PlayAnimation(AnimStateType type, Action endAction = null, float dur = 0.2f)
        {
            PlayAnimation(type.ToString(), endAction, dur);
        }

        public void PlayAnimation(string name, Action endAction = null, float dur = 0.2f)
        {
            if (m_GPUAnim != null)
            {
                m_GPUAnim.PlayAnimation(name, endAction, dur);
            }
            else if (m_Animancer != null)
            {
                if (m_Animancer.animSO.GetClipByState(name) != null)
                {
                    GetStateEvents(name).OnEnd = endAction;
                    m_CurAnimancerState =
                        m_Animancer.Play(m_Animancer.animSO.GetClipByState(name), dur, FadeMode.FromStart);
                    m_CurAnimancerState.Events = GetStateEvents(name);
                }
            }
        }

        public void PlayHighLayerAnimation(AnimStateType type, Action endAction = null,
            float spd = 1f, float dur = 0.1f)
        {
            PlayHighLayerAnimation(type.ToString(), endAction, spd, dur);
        }

        public void PlayHighLayerAnimation(string name, Action endAction = null, float spd = 1f,
            float dur = 0.1f)
        {
            // Log.e(name);
            if (m_Animancer != null && m_Animancer.animSO.GetClipByState(name) != null && m_HighLayer != null)
            {
                if (endAction != null)
                {
                    if (m_DictHighLayerAnimancerAnimEndActions.ContainsKey(name))
                        m_DictHighLayerAnimancerAnimEndActions[name] = endAction;
                    else
                        m_DictHighLayerAnimancerAnimEndActions.Add(name, endAction);
                }

                if (GetStateEvents(name).OnEnd == null && !m_Animancer.animSO.GetClipByState(name).IsLooping)
                {
                    GetStateEvents(name).OnEnd = () =>
                    {
                        if (m_DictHighLayerAnimancerAnimEndActions.ContainsKey(name))
                            m_DictHighLayerAnimancerAnimEndActions[name]?.Invoke();
                        if (m_HighLayer != null)
                            m_HighLayer.StartFade(0, 0.1f);
                        if (m_CurHighLayerAnimancerState != null)
                            m_CurHighLayerAnimancerState.Speed = 1f;
                        m_CurHighLayerAnimancerState = null;
                    };
                }

                m_CurHighLayerAnimancerState =
                    m_HighLayer.Play(m_Animancer.animSO.GetClipByState(name), dur, FadeMode.FromStart);
                m_CurHighLayerAnimancerState.Speed = spd;
                m_CurHighLayerAnimancerState.Events = GetStateEvents(name);
            }
        }

        public void ResetHighLayerAnimation(float duration = 0.2f, float newSpd = 1f)
        {
            if (m_Animancer == null)
                return;

            if (m_HighLayer != null)
                m_HighLayer.StartFade(0, duration);
            if (m_CurHighLayerAnimancerState != null)
                m_CurHighLayerAnimancerState.Speed = newSpd;
            m_CurHighLayerAnimancerState = null;
        }

        public void PlayGlintEffect()
        {
            if (m_GPUAnim != null)
                m_GPUAnim.PlayGlintAnimation();
            else if (m_Animancer != null && m_Animancer.animSO.GetClipByState("Hit") != null)
            {
                if (m_CurHighLayerAnimancerState == null)
                {
                    PlayHighLayerAnimation("Hit");
                }
            }
        }

        public void PlayGrey(float duration)
        {
            if (m_GPUAnim != null)
                m_GPUAnim.PlayGrey(duration);
        }

        public void PlayDissolveEffect(float duration, Action complete, float delay = 1.0f)
        {
            if (m_GPUAnim != null)
                m_GPUAnim.PlayDissolveAnimation(duration, complete, delay);
        }

        #endregion

        #region evt

        private Dictionary<float, Dictionary<string, Action>> m_DictStateEvts =
            new Dictionary<float, Dictionary<string, Action>>();

        void ShowHitEffect(int key, params object[] args)
        {
            PlayGlintEffect();
        }

        public void AddAnimEvt(AnimStateType type, float length, Action evtCallback)
        {
            AddAnimEvt(type.ToString(), length, evtCallback);
        }

        public void AddAnimEvt(string state, float time, Action evtAction)
        {
            if (!IsAnimSupported(state))
                return;

            if (m_GPUAnim != null)
            {
                m_GPUAnim.AddAnimEvt(state, time, evtAction);
            }
            else if (m_Animancer != null)
            {
                if (!m_DictStateEvts.ContainsKey(time))
                {
                    m_DictStateEvts.Add(time, new Dictionary<string, Action>());
                }

                if (m_DictStateEvts[time].ContainsKey(state))
                {
                    Log.e(string.Format("已经设过{0}时间点{1}的事件，不允许重复设定", state, time));
                }
                else
                {
                    m_DictStateEvts[time].Add(state, evtAction);
                    GetStateEvents(state).Add(time, evtAction);
                }
            }
        }

        public void SetAnimEvt(AnimStateType type, int index, Action evtCallback)
        {
            SetAnimEvt(type.ToString(), index, evtCallback);
        }

        public void SetAnimEvt(string state, int index, Action evtAction)
        {
            if (m_Animancer != null)
            {
                GetStateEvents(state).SetCallback(index, evtAction);
            }
        }

        public AnimancerEvent.Sequence GetStateEvents(string state)
        {
            if (m_Animancer == null)
                return null;

            if (m_Animancer.animSO.GetClipByState(state) != null)
            {
                if (!m_DictAnimStateEvts.ContainsKey(state))
                {
                    m_DictAnimStateEvts.Add(state,
                        new AnimancerEvent.Sequence(m_Animancer.animSO.GetClipByState(state).Events));
                }

                return m_DictAnimStateEvts[state];
            }

            return null;
        }

        public AnimancerEvent.Sequence GetHighLayerStateEvents(string state)
        {
            if (m_Animancer.animSO.GetClipByState(state) != null)
            {
                if (!m_DictHighLayerAnimStateEvts.ContainsKey(state))
                {
                    m_DictHighLayerAnimStateEvts.Add(state,
                        new AnimancerEvent.Sequence(m_Animancer.animSO.GetClipByState(state).Events));
                }

                return m_DictHighLayerAnimStateEvts[state];
            }

            return null;
        }

        #endregion

        #region spd

        public void SetAnimSpeed(float spd)
        {
            if (m_GPUAnim != null)
            {
                m_GPUAnim.SetSpeed(spd);
            }
            else if (m_Animancer != null && m_CurAnimancerState != null)
            {
                m_CurAnimancerState.Speed = spd;
            }
        }

        public void SetHighLayerAnimSpeed(float spd)
        {
            if (m_Animancer != null && m_CurHighLayerAnimancerState != null)
            {
                m_CurHighLayerAnimancerState.Speed = spd;
            }
        }

        #endregion
    }
}