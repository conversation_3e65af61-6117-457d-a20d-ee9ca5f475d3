using UnityEngine;
using Qarth;
using Sirenix.OdinInspector;

namespace GameWish.Game
{
    public class SoldierLongBowCtrller : SoldierCtrller
    {
        [SerializeField, LabelText("额外射击触发次数")]
        private int m_MultiShootCount = 6;

        [SerializeField, LabelText("额外射击数量")] private int m_MultiShootArrowCount = 3;

        private int m_MultiShootCounter;

        private RangeAtkModifier m_BulletModifier;

        public override void Register()
        {
            base.Register();
            m_MultiShootCounter = 0;

            if (m_MultiShootArrowCount > 0)
            {
                m_BulletModifier = RangeAtkModifier.Allocate();
            }
        }

        public override void DestroySelf()
        {
            base.DestroySelf();
            if (m_BulletModifier != null)
                ObjectPool<RangeAtkModifier>.S.Recycle(m_BulletModifier);
        }

        protected override void OnAttackTrigger(int key, params object[] args)
        {
            if (args != null && args.Length > 0)
            {
                if ((int) args[0] == entityID)
                {
                    if (m_AIComponent != null && m_AIComponent.aiTarget != null)
                    {
                        m_MultiShootCounter++;
                        if (m_MultiShootCount > 0 && m_MultiShootCounter >= m_MultiShootCount)
                        {
                            StartCoroutine(BulletCreator.AsyncCreateBullet(this,
                                TDBulletConfTable.GetData(m_Conf.atkBulletId),
                                runtimeData.CreateAtkDmgPack(),
                                null,
                                transform.position + Vector3.up + transform.forward * m_Col.radius / 2f,
                                m_AIComponent.aiTarget.GetEntityTransform(),
                                m_AIComponent.aiTarget.GetEntityPosition(),
                                HomeMgr.S.GetOpponentTargetLayerMask(gameObject), null, null));
                            m_MultiShootCounter = 0;
                        }
                        else
                        {
                            StartCoroutine(BulletCreator.AsyncCreateBullet(this,
                                TDBulletConfTable.GetData(m_Conf.atkBulletId),
                                runtimeData.CreateAtkDmgPack(),
                                null,
                                transform.position + Vector3.up + transform.forward * m_Col.radius / 2f,
                                m_AIComponent.aiTarget.GetEntityTransform(),
                                m_AIComponent.aiTarget.GetEntityPosition(),
                                HomeMgr.S.GetOpponentTargetLayerMask(gameObject)));
                        }
                    }
                }
            }
        }
    }
}