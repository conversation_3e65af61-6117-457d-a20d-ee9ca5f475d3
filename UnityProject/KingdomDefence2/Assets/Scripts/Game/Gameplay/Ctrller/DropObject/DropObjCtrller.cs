using System;
using System.Collections;
using PrimeTween;
using Lean.Pool;
using Qarth;
using UnityEngine;

namespace GameWish.Game
{
    public class DropObjCtrller : MonoBehaviour
    {
        public float rotSpd = 100;
        [SerializeField] protected Animation m_Anim;
        protected bool m_Opened;
        protected bool m_Hided;

        public bool isHiding => m_Hided;

        protected GetObjectEntry m_Entry;

        protected virtual void Update()
        {
            if (rotSpd != 0)
                transform.Rotate(Vector3.up * Time.deltaTime * rotSpd);
        }

        protected void OnTriggerEnter(Collider other)
        {
            if (HomeMgr.S.IsTriggerHeroMain(other.gameObject))
            {
                m_Entry = WorldNormalUIPanel.S.ShowGetEntry(
                    transform.position, () =>
                    {
                        DoTriggerShow();
                    });
            }
        }

        protected virtual void DoTriggerShow()
        {
            DropObjMgr.S.SetCurInteractiveObj(this);
        }

        protected void OnTriggerExit(Collider other)
        {
            if (HomeMgr.S.IsTriggerHeroMain(other.gameObject))
            {
                CleanEntry();
            }
            DoTriggerExit();
        }

        protected virtual void DoTriggerExit() { }

        protected void CleanEntry()
        {
            if (m_Entry != null)
                WorldNormalUIPanel.S.HideGetEntry(m_Entry);
            m_Entry = null;
        }

        public void Init()
        {
            if (m_Anim != null)
            {
                m_Anim.Stop();
                m_Anim.Rewind();
                m_Anim.Play();
            }
            m_Hided = false;
        }

        public void Hide()
        {
            if (m_Hided)
                return;
            m_Hided = true;
            CleanEntry();
            LeanPool.Despawn(gameObject);
        }
    }
}