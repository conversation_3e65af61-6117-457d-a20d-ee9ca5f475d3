using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Profiling;

namespace GameWish.Game
{
    public class QuadTree<T> where T : IRect
    {
        private static int MAX_Threshold = 4; //节点孩子数量阈值

        #region  private member
        private Rect m_RectBounds;
        private int m_TreeDepth;
        private float m_MinNodeSize;
        QuadTreeNode<T> m_RootNode;
        #endregion

        public QuadTreeNode<T> RootNode => m_RootNode;
        public int TreeDepth => m_TreeDepth;


        private Queue<QuadTreeNode<T>> m_CheckMergeNodes;
        private Queue<QuadTreeNode<T>> m_NextCheckMergeNodes;

        private List<T> m_CheckContentList;
        private Queue<T> m_NextCheckContentList;


        private bool m_DelayFrame = true;//是否分帧处理
        private int m_DealCount = 10;//一帧只处理5个
        private int m_LastIndex = 0;

        public QuadTree(Rect treeBounds, int maxDepth)
        {
            m_RectBounds = treeBounds;
            m_TreeDepth = maxDepth;
            QuadTreeNode<T>.MAX_DEPTH = m_TreeDepth;
            QuadTreeNode<T>.MAX_OBJECTS = MAX_Threshold;
            m_RootNode = new QuadTreeNode<T>(0, m_RectBounds);
            m_RootNode.GetID();
            QuadTreeNode<T>.NodeMap.Add(m_RootNode.id, m_RootNode);

            m_CheckMergeNodes = new();
            m_NextCheckMergeNodes = new();
            m_CheckMergeNodes.Enqueue(m_RootNode);

            m_CheckContentList = new();
            m_NextCheckContentList = new();
        }

        #region public method
        public void Insert(T item)
        {
            Profiler.BeginSample("QuadTree.Insert");
            m_RootNode.Insert(item);
            m_CheckContentList.Add(item);
            Profiler.EndSample();
        }

        public void Remove(T item)
        {
            Profiler.BeginSample("QuadTree.Remove");
            // m_RootNode.QuickRemove(item);
            m_RootNode.Remove(item);
            m_CheckContentList.Remove(item);
            Profiler.EndSample();
        }

        public void Clear()
        {
            m_RootNode.Clear();
        }

        public void Update()
        {
            Profiler.BeginSample("QuadTree.Update");
            CheckItem();
            CheckMerge();
            Profiler.EndSample();
        }

        public void GetOverlapList(Rect rect, ref List<QuadTreeNode<T>> list)
        {
            list.Clear();
            m_RootNode.GetIndexes(ref list, rect);

        }

        #endregion

        void CheckItem()
        {
            Profiler.BeginSample("QuadTree.CheckItem");

            m_NextCheckContentList.Clear();

            if (m_DelayFrame && m_CheckContentList.Count >= m_DealCount)//分帧
            {
                for (int i = 0; i < m_DealCount; i++)
                {
                    if (m_LastIndex >= m_CheckContentList.Count) continue;
                    var item = m_CheckContentList[m_LastIndex];

                    var itemRect = item.GetRect();
                    var checkRect = item.CheckRect;
                    if (itemRect.x <= checkRect.x ||
                        itemRect.y <= checkRect.y ||
                        itemRect.x + itemRect.width >= checkRect.x + checkRect.width ||
                        itemRect.y + itemRect.height >= checkRect.y + checkRect.height)
                    {
                        m_NextCheckContentList.Enqueue(item);
                    }
                    // if (!QuadTreeHelper.RectContain( item.GetRect(), item.CheckRect))
                    // {
                    //     m_NextCheckContentList.Enqueue(item);
                    // }
                    m_LastIndex++;
                    if (m_LastIndex >= m_CheckContentList.Count)
                    {
                        m_LastIndex = 0;
                    }
                }
            }
            else
            {
                //TODO 如果不分帧的话 可以使用job来加速
                var enumerator = m_CheckContentList.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    var item = enumerator.Current;

                    var itemRect = item.GetRect();
                    var checkRect = item.CheckRect;
                    if (itemRect.x <= checkRect.x ||
                        itemRect.y <= checkRect.y ||
                        itemRect.x + itemRect.width >= checkRect.x + checkRect.width ||
                        itemRect.y + itemRect.height >= checkRect.y + checkRect.height)
                    {
                        m_NextCheckContentList.Enqueue(item);
                    }
                    // if (!QuadTreeHelper.RectContain(item.GetRect(), item.CheckRect))
                    // {
                    //     m_NextCheckContentList.Enqueue(item);
                    // }
                }
            }

            // Debug.LogError($"CheckItem:{m_NextCheckContentList.Count}");

            while (m_NextCheckContentList.Count > 0)
            {
                var item = m_NextCheckContentList.Dequeue();
                Remove(item);
                Insert(item);
            }
            Profiler.EndSample();
        }


        void CheckMerge()
        {
            // Debug.LogError($"CheckMerge:{m_CheckMergeNodes.Count}");
            while (m_CheckMergeNodes.Count > 0)
            {
                var node = m_CheckMergeNodes.Dequeue();
                if (node.parentMerged) continue;
                //内部用来检测是否合并
                var parentNode = node.parentNode;
                if (parentNode != null && parentNode.CanMerge())
                {
                    // 如果所有子节点都是叶子节点的话就可以合并
                    bool allChildAreLeaf = true;
                    for (int i = 0; i < 4; i++)
                    {
                        if (parentNode.Children[i].IsLeaf() == false)
                            allChildAreLeaf = false;
                    }

                    if (allChildAreLeaf)
                    {
                        // Debug.LogError($"Merge");
                        m_NextCheckMergeNodes.Enqueue(parentNode);
                        parentNode.Merge();
                        continue;
                    }
                }


                if (node.IsLeaf())
                {
                    m_NextCheckMergeNodes.Enqueue(node);
                }
                else
                {
                    // if (node.hasSplited)
                    for (int i = 0; i < 4; i++)
                    {
                        m_NextCheckMergeNodes.Enqueue(node.Children[i]);
                    }
                }
            }


            //交换Current和Next
            (m_CheckMergeNodes, m_NextCheckMergeNodes) = (m_NextCheckMergeNodes, m_CheckMergeNodes);

        }



        #region Editor
        public static void ActionDrawNodeGizmo(QuadTreeNode<T> node)
        {
            Rect rect = node.RectBounds;

            var color = new Color(0, (node.depth) * 0.1f, 0, 1);
            Gizmos.color = color;

            if (!node.IsEmpty())
            {
                Gizmos.DrawWireCube(new Vector3(rect.center.x, -10, rect.center.y), new Vector3(rect.width, 10, rect.height));
                DrawNodeTreeContents(node);
            }

            if (node.hasSplited)
            {
                for (int i = 0; i < node.Children.Length; ++i)
                {
                    ActionDrawNodeGizmo(node.Children[i]);
                }
            }
        }

        static void DrawNodeTreeContents(QuadTreeNode<T> node)
        {
            foreach (var item in node.Contents)
            {
                Gizmos.color = Color.white;
                var rect = item.GetRect();
                Gizmos.DrawWireCube(new Vector3(rect.center.x, 0, rect.center.y), new Vector3(rect.width, 2, rect.height));

                // Gizmos.color = Color.red;
                // rect = item.CheckRect;
                // Gizmos.DrawWireCube(new Vector3(rect.center.x, 0, rect.center.y), new Vector3(rect.width, 2, rect.height));
            }
        }
        #endregion
    }
}
