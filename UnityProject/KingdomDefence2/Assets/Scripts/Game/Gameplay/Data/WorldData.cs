using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using UnityEditor;

namespace GameWish.Game
{
    public class WorldData : IDataClass
    {
        public string strFirstStartTime;
        public int passDay = -1;

        public PackData packData;
        public BuildingData buildingData;
        public TaskData taskData;
        public HeroData heroData;
        public BattleData battleData;
        public ResearchData researchData;
        public EquipmentData equipmentData;
        public TreasureData treasureData;
        public OnlineData onlineData;
        public GlobalSkillData globalSkillData;
        public SweepData sweepData;
        public AltarData altarData;
        public RogueSkillData rogueSkillData;

        public int newVersion = 0;

        private int m_LastestVersion = 10001;
        private int m_LastestVersion2 = 10002;

        // public ChopData chopData;
        public WorldData()
        {
            SetDirtyRecorder(WorldInfoMgr.dataDirtyRecorder);
        }

        public override void InitWithEmptyData()
        {
            Log.w("InitWithEmptyData");
            newVersion = m_LastestVersion2;
        }

        public override void OnDataLoadFinish()
        {
            if (newVersion < m_LastestVersion)
            {
                if (packData != null)
                    packData.CleanDataExceptPlayer();

                taskData = new TaskData();
                buildingData = new BuildingData();
                heroData = new HeroData();
                newVersion = m_LastestVersion;
                SetDataDirty();
            }
            if (newVersion < m_LastestVersion2)
            {
                heroData.CheckHeroGetState();
                newVersion = m_LastestVersion2;
                SetDataDirty();
            }

            if (string.IsNullOrEmpty(strFirstStartTime))
                strFirstStartTime = DateTime.Now.Date.ToString(System.Globalization.CultureInfo.InvariantCulture);
            // Log.e(strFirstStartTime);

            if (packData == null)
                packData = new PackData();
            packData.SetDirtyRecorder(m_Recorder);

            if (buildingData == null)
                buildingData = new BuildingData();
            buildingData.HandleStarOnLoad();
            buildingData.SetDirtyRecorder(m_Recorder);

            if (taskData == null)
                taskData = new TaskData();
            taskData.SetDirtyRecorder(m_Recorder);
            taskData.DoOnLoad();

            if (equipmentData == null)
                equipmentData = new EquipmentData();
            equipmentData.SetDirtyRecorder(m_Recorder);
            equipmentData.DoOnLoad();

            if (treasureData == null)
                treasureData = new TreasureData();
            treasureData.SetDirtyRecorder(m_Recorder);
            treasureData.DoOnLoad();

            if (heroData == null)
                heroData = new HeroData();
            heroData.SetDirtyRecorder(m_Recorder);

            if (battleData == null)
                battleData = new BattleData();
            battleData.SetDirtyRecorder(m_Recorder);
            battleData.DoOnLoad();

            if (researchData == null)
                researchData = new ResearchData();
            researchData.SetDirtyRecorder(m_Recorder);

            if (onlineData == null)
                onlineData = new OnlineData();
            onlineData.SetDirtyRecorder(m_Recorder);
            onlineData.DoOnload();

            if (globalSkillData == null)
                globalSkillData = new GlobalSkillData();
            globalSkillData.CheckSkillSlot();
            globalSkillData.SetDirtyRecorder(m_Recorder);
            
            if (altarData == null)
                altarData = new AltarData();
            altarData.SetDirtyRecorder(m_Recorder);
            
            if (rogueSkillData == null)
                rogueSkillData = new RogueSkillData();
            rogueSkillData.SetDirtyRecorder(m_Recorder);

            if (sweepData == null)
            {
                sweepData = new SweepData();
            }
            sweepData.SetDirtyRecorder(m_Recorder);

            ResetDailyData();
        }

        public int GetPassDay()
        {
            DateTime time = DateTime.Parse(strFirstStartTime, System.Globalization.CultureInfo.InvariantCulture);

            var ts = DateTime.Now - time;
            return ts.Days;
        }

        public void ResetDailyData()
        {
            if (GetPassDay() > passDay)
            {
                passDay = GetPassDay();
                taskData.ResetTask();
                heroData.ResetDaily();
                treasureData.ResetDailyData();
                onlineData.ResetDaily();
                sweepData.ResetDaily();
                EventSystem.S.Send(EventID.OnNewDay);
                SetDataDirty();
            }
        }

        public void CleanCurrentWorldData(bool isComplete)
        {
            buildingData.CleanData(isComplete);
            packData.CleanDataExceptPlayer();
            altarData.ResetAllAltarData();
            
            if (isComplete)
                battleData.CleanData();
        }
    }
}