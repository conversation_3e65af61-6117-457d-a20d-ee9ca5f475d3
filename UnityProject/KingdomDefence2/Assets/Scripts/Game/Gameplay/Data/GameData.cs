using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class GameData : IDataClass
    {
        public string strFirstStartTime;
        public string lastPlayTimeString = "0";
        public int passDay = -1;
        public int loginDay = 0;

        public EInt dailyAdCount = 0;
        public EInt totalAdCount = 0;

        public GuideData guideData;
        public BonusData bonusData;
        public SignData signData;
        public ShoppingData shoppingData;
        public PlayerData playerData;
        public PerimeterSystemData perimeterSystemData;
        public SourceEvtRecordData sourceEvtRecordData;


        public List<int> lstRewardedAdRewardIds = new();

        public GameData()
        {
            SetDirtyRecorder(PlayerInfoMgr.dataDirtyRecorder);
        }

        public override void InitWithEmptyData()
        {
            Log.w("InitWithEmptyData");
            SetDataDirty();
        }

        public override void OnDataLoadFinish()
        {
            LoadSubData();
            ResetDailyData();

            if (shoppingData != null &&
                !shoppingData.promoNewbie &&
                signData != null &&
                signData.signIndex > 0 &&
                GuideModule.GuideIsFinish())
            {
                shoppingData.SetNewbiePromoted();
            }
        }

        void LoadSubData()
        {
            if (string.IsNullOrEmpty(strFirstStartTime))
            {
                strFirstStartTime = DateTime.Today.ToString(System.Globalization.CultureInfo.InvariantCulture);
            }

            if (guideData == null)
                guideData = new GuideData();
            guideData.SetDirtyRecorder(m_Recorder);
            if (bonusData == null)
                bonusData = new BonusData();
            bonusData.SetDirtyRecorder(m_Recorder);
            bonusData.DoOnload();

            if (signData == null)
                signData = new SignData();
            signData.SetDirtyRecorder(m_Recorder);

            if (shoppingData == null)
                shoppingData = new ShoppingData();
            shoppingData.SetDirtyRecorder(m_Recorder);
            shoppingData.DoOnload();

            if (playerData == null)
                playerData = new();
            playerData.SetDirtyRecorder(m_Recorder);
            playerData.DoOnload();

            if (perimeterSystemData == null)
                perimeterSystemData = new();
            perimeterSystemData.SetDirtyRecorder(m_Recorder);
            // perimeterSystemData.DoOnload();

            if (sourceEvtRecordData == null)
            {
                sourceEvtRecordData = new SourceEvtRecordData();
            }

            sourceEvtRecordData.Init();

            SetDataDirty();
        }

        public void SetLastPlayTime(string time)
        {
            if (long.Parse(time) > long.Parse(lastPlayTimeString))
            {
                lastPlayTimeString = time;
                SetDataDirty();
            }
        }

        public int GetPassDay()
        {
            DateTime time = DateTime.Parse(strFirstStartTime, System.Globalization.CultureInfo.InvariantCulture);
            var ts = DateTime.Today - time;
            return ts.Days;
        }

        public void ResetDailyData()
        {
            int passedDay = GetPassDay();
            if (passDay == -1 || passedDay > passDay)
            {
                dailyAdCount = 0;
                bonusData.ResetDaily();
                signData.ResetDaily();
                passDay = passedDay;
                loginDay++;
                shoppingData.ResetDaily();
                SetDataDirty();
            }
        }

        #region [Ad]

        public void AddPlayAdCount(Vector3 pos = default)
        {
            totalAdCount++;
            dailyAdCount++;
            SetDataDirty();

            EventSystem.S.Send(EventID.OnAdCounted, pos);
        }

        public void RecordAdReward(int id)
        {
            if (!lstRewardedAdRewardIds.Contains(id))
            {
                lstRewardedAdRewardIds.Add(id);
                SetDataDirty();

                EventSystem.S.Send(EventID.OnAdRewarded);
            }
        }

        #endregion
    }
}