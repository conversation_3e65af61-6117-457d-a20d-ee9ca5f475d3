using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDBuildingInfoConfTable
    {
        private static int[] m_LstBookBuildingConfs;

        public static int[] GetBookBuildingConfs()
        {
            if (m_LstBookBuildingConfs == null)
            {
                List<int> results = new List<int>();
                for (int i = 0; i < dataList.Count; i++)
                {
                    if (dataList[i].isInBook)
                    {
                        results.Add(dataList[i].id);
                    }
                }

                m_LstBookBuildingConfs = results.ToArray();
            }

            return m_LstBookBuildingConfs;
        }

        private static Dictionary<int, List<int>> m_DictTowerShowBuildingIds;

        public static Dictionary<int, List<int>> GetTowerShowBuildingIds()
        {
            if (m_DictTowerShowBuildingIds == null)
            {
                m_DictTowerShowBuildingIds = new Dictionary<int, List<int>>();
                for (int i = 0; i < dataList.Count; i++)
                {
                    if (!dataList[i].isInBook)
                        continue;

                    switch (dataList[i].buildingType)
                    {
                        case "ArrowTower":
                        {
                            var key = (int)TowerType.ArrowTower;
                            if (!m_DictTowerShowBuildingIds.ContainsKey(key))
                            {
                                m_DictTowerShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictTowerShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;

                        case "BombTower":
                        {
                            var key = (int)TowerType.BombTower;
                            if (!m_DictTowerShowBuildingIds.ContainsKey(key))
                            {
                                m_DictTowerShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictTowerShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;

                        case "MagicTower":
                        {
                            var key = (int)TowerType.MagicTower;
                            if (!m_DictTowerShowBuildingIds.ContainsKey(key))
                            {
                                m_DictTowerShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictTowerShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;
                    }
                }
            }

            return m_DictTowerShowBuildingIds;
        }

        private static Dictionary<int, List<int>> m_DictBarrackShowBuildingIds;

        public static Dictionary<int, List<int>> GetBarrackShowBuildingIds()
        {
            if (m_DictBarrackShowBuildingIds == null)
            {
                m_DictBarrackShowBuildingIds = new Dictionary<int, List<int>>();
                for (int i = 0; i < dataList.Count; i++)
                {
                    if (!dataList[i].isInBook)
                        continue;

                    switch (dataList[i].buildingType)
                    {
                        case "BarracksWarrior":
                        {
                            var key = (int)BarrackType.BarracksWarrior;
                            if (!m_DictBarrackShowBuildingIds.ContainsKey(key))
                            {
                                m_DictBarrackShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictBarrackShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;
                        case "BarracksArchor":
                        {
                            var key = (int)BarrackType.BarracksArchor;
                            if (!m_DictBarrackShowBuildingIds.ContainsKey(key))
                            {
                                m_DictBarrackShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictBarrackShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;
                        case "BarracksMage":
                        {
                            var key = (int)BarrackType.BarracksMage;
                            if (!m_DictBarrackShowBuildingIds.ContainsKey(key))
                            {
                                m_DictBarrackShowBuildingIds.Add(key, new List<int>());
                            }

                            m_DictBarrackShowBuildingIds[key].Add(dataList[i].id);
                        }
                            break;
                    }
                }
            }

            return m_DictBarrackShowBuildingIds;
        }

        private static List<int> m_OtherBuildingIds;

        public static List<int> GetOtherShowBuildingIds()
        {
            if (m_OtherBuildingIds == null)
            {
                m_OtherBuildingIds = new();
                for (int i = 0; i < dataList.Count; i++)
                {
                    if (!dataList[i].isInBook)
                        continue;

                    switch (dataList[i].buildingType)
                    {
                        case "MainHall":
                        case "Logging":
                        case "DefenceWall":
                            m_OtherBuildingIds.Add(dataList[i].id);
                            break;
                    }
                }
            }

            return m_OtherBuildingIds;
        }


        private static List<int> _AllWeightList = new();
        public static List<int> GetAllWeightList() => _AllWeightList;

        static void CompleteRowAdd(TDBuildingInfoConf tdData)
        {
            _AllWeightList.Add(tdData.rate);
        }
    }
}