using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDAdRewardConf
    {
        public List<ItemTypeEnum> lstRewardTypes = new();
        public List<int> lstRewardCounts = new();
        public List<int> lstRewardIndexs = new();
        
        public void Reset()
        {
            var strTypes = Helper.String2ListInt(rewardType, "|");
            for (int i = 0; i < strTypes.Count; i++)
            {
                lstRewardTypes.Add((ItemTypeEnum)strTypes[i]);
            }
            
            lstRewardCounts = Helper.String2ListInt(rewardCount, "|");
            lstRewardIndexs = Helper.String2ListInt(rewardIndex, "|");
        }
    }
}