//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDItemConf
    {
      
        private EInt m_Id = 0;
        private string m_Icon;
        private string m_Bg;
        private string m_Prefab;
        private string m_ItemName;
        private string m_ItemDes;
      
      //private Dictionary<string, TDUniversally.FieldData> m_DataCacheNoGenerate = new Dictionary<string, TDUniversally.FieldData>();
      
        /// <summary>
        /// ID
        /// </summary>
        public int id {get { return m_Id; } }
      
        /// <summary>
        /// 图标
        /// </summary>
        public string icon {get { return m_Icon; } }
      
        /// <summary>
        /// 背景图
        /// </summary>
        public string bg {get { return m_Bg; } }
      
        /// <summary>
        /// 可加载预制体
        /// </summary>
        public string prefab {get { return m_Prefab; } }
      
        /// <summary>
        /// 名称
        /// </summary>
        public string itemName {get { return m_ItemName; } }
      
        /// <summary>
        /// 描述
        /// </summary>
        public string itemDes {get { return m_ItemDes; } }

        public void ReadRow(DataStreamReader dataR, int[] filedIndex)
        {
          //var schemeNames = dataR.GetSchemeName();
          int col = 0;
          while(true)
          {
            col = dataR.MoreFieldOnRow();
            if (col == -1)
            {
              break;
            }
            switch (filedIndex[col])
            { 
                case 0:
                    m_Id = dataR.ReadInt();
                    break;
                case 1:
                    m_Icon = dataR.ReadString();
                    break;
                case 2:
                    m_Bg = dataR.ReadString();
                    break;
                case 3:
                    m_Prefab = dataR.ReadString();
                    break;
                case 4:
                    m_ItemName = dataR.ReadString();
                    break;
                case 5:
                    m_ItemDes = dataR.ReadString();
                    break;
                default:
                    //TableHelper.CacheNewField(dataR, schemeNames[col], m_DataCacheNoGenerate);
                    break;
            }
          }

          // 初始化枚举值，提前完成转换
          InitEnumValues();
        }
        
        // 初始化所有枚举值，提高性能
        private void InitEnumValues()
        {
        }
        
        /*
        public DataStreamReader.FieldType GetFieldTypeInNew(string fieldName)
        {
            if (m_DataCacheNoGenerate.ContainsKey(fieldName))
            {
                return m_DataCacheNoGenerate[fieldName].fieldType;
            }
            return DataStreamReader.FieldType.Unkown;
        }
        */
        
        public static Dictionary<string, int> GetFieldHeadIndex()
        {
          Dictionary<string, int> ret = new Dictionary<string, int>(6);
          
          ret.Add("Id", 0);
          ret.Add("Icon", 1);
          ret.Add("Bg", 2);
          ret.Add("Prefab", 3);
          ret.Add("ItemName", 4);
          ret.Add("ItemDes", 5);
          return ret;
        }
    }
}//namespace