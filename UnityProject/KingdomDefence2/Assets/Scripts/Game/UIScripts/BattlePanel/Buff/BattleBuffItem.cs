using System.Collections;
using System.Collections.Generic;
using PrimeTween;
using UnityEngine;
using UnityEngine.UI;
using Qarth;

namespace GameWish.Game
{
    public class BattleBuffItem : MonoBehaviour
    {
        [SerializeField] private Text m_TxtName;
        [SerializeField] private Image m_ImgFrame;
        [SerializeField] private Image m_ImgBG;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private Text m_TxtDesc;
        [SerializeField] private Button m_BtnClick;
        [SerializeField] private Button m_BtnExtra;
        [SerializeField] private GameObject m_ObjectSelect;

        [SerializeField] private GameObject m_ObjType;
        [SerializeField] private Image m_ImgType;
        [SerializeField] private Text m_TxtTypeCount;

        [SerializeField] private ParticleSystem m_PSFront;

        private TDBattleBuffConf m_Conf;
        private TDBattleBuffGroupConf m_GroupConf;
        private AbstractAnimPanel m_Panel;
        private bool m_Selected = false;

        private int m_SelectId;
        private int m_SubId;

        private void OnEnable()
        {
            m_BtnClick.onClick.AddListener(OnClick);
            m_BtnExtra.onClick.AddListener(OnClickExtra);
        }

        private void OnDisable()
        {
            m_BtnClick.onClick.RemoveAllListeners();
            m_BtnExtra.onClick.RemoveAllListeners();
        }

        public void SetInfo(AbstractAnimPanel panel, int buffId, int subId)
        {
            m_Panel = panel;
            m_Conf = TDBattleBuffConfTable.GetData(buffId);
            m_GroupConf = TDBattleBuffGroupConfTable.GetData(buffId);

            m_Selected = false;
            m_ObjectSelect.SetActive(false);
            m_ObjType.SetActive(false);
            m_PSFront.gameObject.SetActive(false);
            m_BtnClick.interactable = true;
            m_ImgFrame.gameObject.SetActive(false);
            m_BtnExtra.gameObject.SetActive(false);

            m_SelectId = buffId;
            m_SubId = subId;

            if (m_Conf != null)
            {
                gameObject.SetActive(true);

                if (subId > 0)
                {
                    panel.FindSpriteAsync(m_Conf.subDrawerInstance.GetDrawedIcon(subId), (spr) =>
                    {
                        m_ImgIcon.sprite = spr;
                        m_ImgIcon.SetNativeSize();
                    }, true);

                    m_TxtName.text = m_Conf.subDrawerInstance.GetDrawedName(subId);
                    m_TxtDesc.text = m_Conf.subDrawerInstance.GetDrawedDesc(subId);
                }
                else
                {
                    panel.FindSpriteAsync(m_Conf.icon, (spr) =>
                    {
                        m_ImgIcon.sprite = spr;
                        m_ImgIcon.SetNativeSize();
                    }, true);

                    m_TxtName.text = TDLanguageTable.Get(m_Conf.name);
                    m_TxtDesc.text = TDLanguageTable.Get(m_Conf.desc);
                }

                // m_TxtDesc.text = HomeMgr.S.GetRogueSkillFuncConfigSO(funcLvlConf.funcConfName).function.GetDesc();
                panel.FindSpriteAsync(BattleBuffMgr.GetBgName(m_Conf.quality), (spr) => { m_ImgBG.sprite = spr; }, true);
                
                if (!string.IsNullOrEmpty(BattleBuffMgr.GetTypeName(m_Conf.classify)))
                {
                    m_ObjType.SetActive(true);
                    panel.FindSpriteAsync(BattleBuffMgr.GetTypeName(m_Conf.classify), (spr) => { m_ImgType.sprite = spr; }, true);
                    m_TxtTypeCount.text = string.Format("{0}", BattleBuffMgr.S.GetBattleBuffCount(m_Conf.classify));
                }
            }
            //如果是组强化
            else if (m_GroupConf != null)
            {
                gameObject.SetActive(true);
                panel.FindSpriteAsync(m_GroupConf.icon, (spr) =>
                {
                    m_ImgIcon.sprite = spr;
                    m_ImgIcon.SetNativeSize();
                }, true);

                m_TxtName.text = TDLanguageTable.Get(m_GroupConf.name);
                m_TxtDesc.text = TDLanguageTable.Get(m_GroupConf.desc);
                // m_TxtDesc.text = HomeMgr.S.GetRogueSkillFuncConfigSO(funcLvlConf.funcConfName).function.GetDesc();

                panel.FindSpriteAsync(BattleBuffMgr.GetBgName(-1), (spr) => { m_ImgBG.sprite = spr; }, true);
                m_ImgFrame.gameObject.SetActive(true);
            }
            else
            {
                gameObject.SetActive(false);
                m_BtnClick.interactable = false;
            }
        }

        public void SetStatus(bool selected, bool extra, bool interactable)
        {
            m_Selected = selected;
            m_ObjectSelect.SetActive(selected);
            // m_BtnExtra.gameObject.SetActive(extra);
            m_BtnClick.interactable = interactable;

            m_PSFront.gameObject.SetActive(selected);
        }


        void OnClick()
        {
            if (m_Conf == null && m_GroupConf == null)
                return;

            AudioMgr.S.PlaySound(AudioID.AUDIO_BUFF);
            m_Selected = true;
            m_ObjectSelect.SetActive(true);
            m_BtnClick.interactable = false;
            SelectBuff();
        }
        
        void OnClickExtra()
        {
            if (m_Conf == null && m_GroupConf == null)
                return;


            AdsPlayMgr.S.PlayRewardAd("BattleBuffExtra", (c) =>
            {
                m_BtnExtra.gameObject.SetActive(false);
                OnClick();
            }, null, m_BtnExtra.transform.position);
        }


        public void SelectBuff()
        {
            BattleBuffMgr.S.AddBattleBuff(m_SelectId, m_SubId);
            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("id", m_SelectId)
                .AddEventParam("subId", m_SubId)
                .CustomEventDic(DataAnalysisID.EVENT_SELECT_BATTLEBUFF, DAPE.ThinkingData);
        }

        public void ShowBonus()
        {
            if ((m_Conf == null && m_GroupConf == null) || m_Selected)
            {
                return;
            }

            m_BtnExtra.gameObject.SetActive(true);
            m_BtnExtra.transform.DOKill();
            m_BtnExtra.transform.localScale = Vector3.zero;
            m_BtnExtra.transform.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutBack);

            m_BtnClick.interactable = false;
        }
    }
}