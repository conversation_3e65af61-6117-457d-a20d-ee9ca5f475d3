using System.Collections;
using System.Collections.Generic;
using Game.Tool;
using PrimeTween;
using UnityEngine;
using Qarth;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class BattleBuffPanel : AbstractAnimPanel
    {
        [SerializeField] private BattleBuffItem m_BuffItemPrefab;
        [SerializeField] private Transform m_BuffItemParent;
        [SerializeField] private Button m_BtnGetAll;
        [SerializeField] private Button m_BtnRefresh;
        [SerializeField] private Button m_BtnRefreshFree;
        [SerializeField] private Button m_BtnContinue;

        private List<BattleBuffItem> m_Items = new();

        private int m_SelectCount = 0;
        private bool m_IsAdv;
        private bool m_HasBonus = false;

        private const string BuffItemName = "BattleBuffItem";

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BuffItemPrefab.gameObject.SetActive(false);
            PoolingMgr.S.AddPoolingUI(BuffItemName, m_BuffItemPrefab.gameObject);

            m_BtnRefreshFree.onClick.AddListener(OnClickRefresh);
            m_BtnContinue.onClick.AddListener(OnClickContinue);
            m_BtnRefresh.onClick.AddListener(OnClickRefresh);
            m_BtnGetAll.onClick.AddListener(OnClickGetAll);

            GameTimeMgr.S.LockGlobalTS();
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            RegisterEvent(EventID.OnBattleBuffAdd, OnBattleBuffAdd);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);

            Dictionary<int, int> ids = null;
            if (args != null && args.Length > 1)
            {
                ids = (Dictionary<int, int>)args[0];
                m_IsAdv = (bool)args[1];
            }
            else
            {
                ids = BattleBuffMgr.S.GenRandomSelection();
                m_IsAdv = false;
            }

            if (!m_IsAdv)
            {
                m_HasBonus = RandomHelper.Range(0f, 1f) < TreasureMgr.S.playerTreasureRuntimeData.rogueSkillFreeRollRadio;
                m_BtnRefreshFree.gameObject.SetActive(m_HasBonus);
                m_BtnRefresh.gameObject.SetActive(!m_HasBonus);

                if (WorldInfoMgr.data.battleData.GetChapterItem(1) != null)
                {
                    if (WorldInfoMgr.data.battleData.GetChapterItem(1).maxWave <= 1)
                    {
                        m_BtnRefreshFree.gameObject.SetActive(false);
                        m_BtnRefresh.gameObject.SetActive(false);
                    }
                }
            }
            else
            {
                m_HasBonus = false;
                m_BtnRefreshFree.gameObject.SetActive(false);
                m_BtnRefresh.gameObject.SetActive(false);
            }

            m_BtnGetAll.gameObject.SetActive(false);
            m_BtnContinue.gameObject.SetActive(false);
            m_SelectCount = 0;

            CreateBuffItem(ids);
            ShowSubUiAnim();

            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("name", "BattleBuffPanel")
                .CustomEventDic(DataAnalysisID.EVENT_PANEL_OPEN, DAPE.ThinkingData);
        }

        protected override void OnClose()
        {
            base.OnClose();
            ClearExistBuffItem();
            StopAllCoroutines();

            GameTimeMgr.S.FreeGlobalTS();
            //
            // EventSystem.S.Send(EventID.OnBattleBuffOver);
            BattleBuffMgr.S.CheckGroupBattleBuff();
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        void ClearExistBuffItem()
        {
            for (int i = 0; i < m_Items.Count; i++)
            {
                PoolingMgr.S.RecycleUIObj(m_Items[i].gameObject);
            }

            m_Items.Clear();
        }


        void CreateBuffItem(Dictionary<int, int> dictIds)
        {
            ClearExistBuffItem();

            foreach (var ids in dictIds)
            {
                if (ids.Key < 0)
                    continue;
                var item = PoolingMgr.S.GetUIPoolItem<BattleBuffItem>(BuffItemName);
                item.transform.SetParent(m_BuffItemParent);
                item.transform.ResetTrans();
                item.SetInfo(this, ids.Key, ids.Value);
                item.gameObject.SetActive(true);
                m_Items.Add(item);
            }
        }

        void OnBattleBuffAdd(int key, params object[] args)
        {
            if (m_IsAdv)
                HideSelfWithAnim();
            else
            {
                m_BtnRefresh.gameObject.SetActive(false);
                m_BtnRefreshFree.gameObject.SetActive(false);
                m_BtnGetAll.gameObject.SetActive(false);

                if (m_SelectCount == 0 && RandomHelper.Range(0f, 1f) < 0.5f)
                {
                    for (int i = 0; i < m_Items.Count; i++)
                    {
                        m_Items[i].ShowBonus();
                    }

                    m_BtnContinue.gameObject.SetActive(true);
                }
                else
                {
                    HideSelfWithAnim();
                }

                m_SelectCount += 1;
            }
        }

        void OnClickRefresh()
        {
            if (m_HasBonus)
            {
                CreateBuffItem(BattleBuffMgr.S.GenRandomSelection());

                m_HasBonus = false;
                m_BtnRefreshFree.gameObject.SetActive(m_HasBonus);
                m_BtnRefresh.gameObject.SetActive(!m_HasBonus);
            }
            else
            {
                AdsPlayMgr.S.PlayRewardAd("BattleBuffRefresh", (c) =>
                {
                    CreateBuffItem(BattleBuffMgr.S.GenRandomSelection());
                }, null, m_BtnRefresh.transform.position);
            }
        }

        void OnClickGetAll()
        {
            AdsPlayMgr.S.PlayRewardAd("BattleBuffGetAll", (c) =>
            {
                for (int i = 0; i < m_Items.Count; i++)
                {
                    m_Items[i].SelectBuff();
                }

                AudioMgr.S.PlaySound(AudioID.AUDIO_BUFF);
                HideSelfWithAnim();
            }, null, m_BtnGetAll.transform.position);
        }

        void OnClickContinue()
        {
            HideSelfWithAnim();
        }
    }
}