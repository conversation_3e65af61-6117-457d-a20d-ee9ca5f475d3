using System;
using System.Collections.Generic;
using Game.Core.UIEffect;
using UnityEngine;
using UnityEngine.UI;
using Qarth;

namespace GameWish.Game
{
    public class AdRewardPanelItem : MonoBehaviour
    {
        [SerializeField] private GraphicGroup m_GGroup;
        
        [SerializeField] private Text m_TxtTitle;
        [SerializeField] private Text m_TxtProgress;
        [SerializeField] private Image m_ImgFill;
        
        [SerializeField] private Text m_TxtBtn;
        [SerializeField] private Button m_BtnClick;

        [SerializeField] private ShopGiftBagItem[] m_Items;

        [SerializeField] private GameObject m_NextTag;

        private TDAdRewardConf m_Conf;
        
        private void OnEnable()
        {
            m_BtnClick.onClick.AddListener(OnClick);
        }
        private void OnDisable()
        {
            m_BtnClick.onClick.RemoveAllListeners();
        }

        private void OnDestroy()
        {
            m_Conf = null;
        }

        public void SetInfo(AbstractPanel panel, TDAdRewardConf conf, bool isLast)
        {
            m_Conf = conf;
            for (int i = 0; i < m_Items.Length; i++)
            {
                if (i < conf.lstRewardTypes.Count)
                {
                    m_Items[i].gameObject.SetActive(true);
                    m_Items[i].SetInfo(panel, conf.lstRewardTypes[i], conf.lstRewardCounts[i], conf.lstRewardIndexs[i]);
                }
                else
                {
                    m_Items[i].gameObject.SetActive(false);
                }
            }

            m_NextTag.gameObject.SetActive(!isLast);

            UpdateInfo();
        }

        void UpdateInfo()
        {
            m_GGroup.Color = PlayerInfoMgr.data.lstRewardedAdRewardIds.Contains(m_Conf.id) ? 
                new Color(0.5f, 0.5f, 0.5f, 1) : Color.white;
            m_ImgFill.fillAmount = PlayerInfoMgr.data.totalAdCount * 1f / m_Conf.needCount;
            m_TxtProgress.text = $"{PlayerInfoMgr.data.totalAdCount}/{m_Conf.needCount}";
            
            m_BtnClick.interactable = PlayerInfoMgr.data.totalAdCount >= m_Conf.needCount;
            m_BtnClick.gameObject.SetActive(!PlayerInfoMgr.data.lstRewardedAdRewardIds.Contains(m_Conf.id));
            m_TxtBtn.text =PlayerInfoMgr.data.totalAdCount >= m_Conf.needCount ? 
                TDLanguageTable.Get("Get") : TDLanguageTable.Get("NotAvailable");
        }

        void OnClick()
        {
            if (m_Conf != null)
            {
                var rewards = new List<RewardInfo>();
                for(int i = 0; i < m_Conf.lstRewardTypes.Count; i++)
                {
                    var rewardInfo = RewardInfo.Allocate();
                    rewardInfo.rewardType = m_Conf.lstRewardTypes[i];
                    rewardInfo.rewardAmount = m_Conf.lstRewardCounts[i];
                    rewardInfo.rewardValue = m_Conf.lstRewardIndexs[i];
                    rewardInfo.src = "adReward";
                    rewards.Add(rewardInfo);
                }

                if (rewards.Count > 0)
                {
                    UIMgr.S.OpenPanel(UIID.RewardPanel, rewards);
                    PlayerInfoMgr.data.RecordAdReward(m_Conf.id);
                    UpdateInfo();
                }
            }
        }
    }
}