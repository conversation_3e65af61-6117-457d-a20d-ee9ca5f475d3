using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using Spine.Unity;

namespace GameWish.Game
{
    public class ChapterPanel : AbstractAnimPanel
    {
        [SerializeField] private Text m_TxtChapterTitle;
        [SerializeField] private Text m_TxtChapterWaveMax;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private SkeletonGraphic m_ChapterSkeleton;
        [SerializeField] private Button m_BtnLeft;
        [SerializeField] private Button m_BtnRight;
        [SerializeField] private Button m_BtnStart;
        [SerializeField] private Text m_TxtCostEnergy;
        [SerializeField] private Text m_TxtChapter;
        [SerializeField] private Button m_BtnSetting;
        [SerializeField] private Button m_BtnCombine;
        [SerializeField] private Button m_BtnBattlePass;
        [SerializeField] private Button m_BtnSweep;
        [SerializeField] private Button m_BtnNewFunc;

        [SerializeField] private Button m_BtnAdReward;
        [SerializeField] private Image m_ImgAdFill;

        [SerializeField] private GamingPlayer m_PlayerInfo;
        [SerializeField] private Transform m_TransFlyExp;
        [SerializeField] private USimpleListView m_ViewRewrdBox;
        [SerializeField] private ElementUIAnimCustomStart m_AnimBtnAcieve;
        [SerializeField] private ElementUIAnimCustomStart m_AnimBtnPass;
        [SerializeField] private ChapterPanel_UnlockFuncTips m_UnlockFuncTips;

        [SerializeField] private ChapterPanel_UnlockHeroLvl m_UnlockHeroLvl;

        private ElementUIAnimCustomStart m_ChapterAnim;
        private ChapterItemData m_CurSelectData;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_ChapterAnim = m_ImgIcon.transform.GetComponent<ElementUIAnimCustomStart>();
            m_TxtCostEnergy.text = $"- {Define.GAME_POWER_COST}";
            m_BtnStart.onClick.AddListener(() =>
            {
                if (PlayerInfoMgr.data.playerData.playerPower >= Define.GAME_POWER_COST)
                {
                    PlayerInfoMgr.data.playerData.UsePlayerPower(Define.GAME_POWER_COST);
                    WorldInfoMgr.data.battleData.ResetDropBagCounter();
                    ChapterMgr.S.StartChapter(m_CurSelectData.chapterId);

                    DataAnalysisMgr.S.ResetEventMap()
                        .AddEventParam("chapter", m_CurSelectData.chapterId)
                        .AddEventParam("capId", WorldInfoMgr.data.heroData.mainHeroId)
                        .CustomEventDic(DataAnalysisID.EVENT_BATTLEWAVE_START, DAPE.ThinkingData);
                }
                else
                {
                    UIMgr.S.OpenPanel(UIID.SupplyPowerPanel);
                }
            });
            m_BtnLeft.onClick.AddListener(() =>
            {
                if (WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId - 1) != null)
                {
                    m_ChapterAnim.ShowAnim();
                    UpdateCurChapterInfo(WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId - 1));
                }
            });
            m_BtnRight.onClick.AddListener(() =>
            {
                if (WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId + 1) != null)
                {
                    m_ChapterAnim.ShowAnim();
                    UpdateCurChapterInfo(WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId + 1));
                }
            });
            m_BtnSetting.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.SettingPanel); });
            m_BtnCombine.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.CombineSignPanel); });
            m_BtnBattlePass.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.BattlePassPanel); });
            m_BtnSweep.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.SweepPanel); });
            m_BtnNewFunc.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.FuncUnlockPanel); });

            m_BtnAdReward.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.AdRewardPanel); });

            m_PlayerInfo.Init();
            m_ViewRewrdBox.SetCellRenderer(OnCellRenderer);
        }

        void OnCellRenderer(Transform root, int index)
        {
            root.GetComponent<ChapterPanel_Box>().SetInfo(index, GetAllBoxReward);
        }

        void GetAllBoxReward()
        {
            Dictionary<int, RewardInfo> dicReward = new Dictionary<int, RewardInfo>();
            List<RewardInfo> listReward = new List<RewardInfo>();
            for (int i = 0; i < WorldInfoMgr.data.battleData.lstChapters.Count; i++)
            {
                var item = WorldInfoMgr.data.battleData.lstChapters[i];
                for (int j = 0; j < item.rewardCompleteId.Count; j++)
                {
                    int completeId = item.rewardCompleteId[j];
                    if (!item.rewardedId.Contains(completeId))
                    {
                        item.GetReward(completeId);
                        var conf = TDBattleChapterConfTable.GetData(item.chapterId);
                        switch (completeId)
                        {
                            case 0:
                                for (int k = 0; k < conf.LstPassRewardType.Length; k++)
                                {
                                    if (dicReward.ContainsKey(conf.LstPassRewardType[k]))
                                    {
                                        dicReward[conf.LstPassRewardType[k]].rewardAmount += conf.LstPassRewardAmount[k];
                                    }
                                    else
                                    {
                                        var rewardInfo = RewardInfo.Allocate();
                                        rewardInfo.rewardType = (ItemTypeEnum)conf.LstPassRewardType[k];
                                        rewardInfo.rewardAmount = conf.LstPassRewardAmount[k];
                                        rewardInfo.src = "battleBox";
                                        listReward.Add(rewardInfo);
                                        dicReward.Add(conf.LstPassRewardType[k], rewardInfo);
                                    }
                                }

                                break;
                            case 1:
                                for (int k = 0; k < conf.LstPlayerAliveRewardType.Length; k++)
                                {
                                    if (dicReward.ContainsKey(conf.LstPlayerAliveRewardType[k]))
                                    {
                                        dicReward[conf.LstPlayerAliveRewardType[k]].rewardAmount += conf.LstPlayerAliveRewardAmount[k];
                                    }
                                    else
                                    {
                                        var rewardInfo = RewardInfo.Allocate();
                                        rewardInfo.rewardType = (ItemTypeEnum)conf.LstPlayerAliveRewardType[k];
                                        rewardInfo.rewardAmount = conf.LstPlayerAliveRewardAmount[k];
                                        rewardInfo.src = "battleBox";
                                        listReward.Add(rewardInfo);
                                        dicReward.Add(conf.LstPlayerAliveRewardType[k], rewardInfo);
                                    }
                                }

                                break;
                            case 2:
                                for (int k = 0; k < conf.LstTowerRewardType.Length; k++)
                                {
                                    if (dicReward.ContainsKey(conf.LstTowerRewardType[k]))
                                    {
                                        dicReward[conf.LstTowerRewardType[k]].rewardAmount += conf.LstTowerRewardAmount[k];
                                    }
                                    else
                                    {
                                        var rewardInfo = RewardInfo.Allocate();
                                        rewardInfo.rewardType = (ItemTypeEnum)conf.LstTowerRewardType[k];
                                        rewardInfo.rewardAmount = conf.LstTowerRewardAmount[k];
                                        rewardInfo.src = "battleBox";
                                        listReward.Add(rewardInfo);
                                        dicReward.Add(conf.LstTowerRewardType[k], rewardInfo);
                                    }
                                }

                                break;
                        }
                    }
                }
            }

            UIMgr.S.OpenPanel(UIID.RewardPanel, null, listReward);

            m_ViewRewrdBox.SetDataCount(WorldInfoMgr.data.battleData.lstChapters.Count * 3);
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            RegisterEvent(EventID.OnCloseIDPanel, (key, args) =>
            {
                if ((int)args[0] == uiID)
                    HideSelfWithAnim();
            });
            // RegisterEvent(EventID.OnPlayerInfoUpdate, OnPlayerInfoUpdate);
            RegisterEvent(EventID.OnGuideFuncUnlocked, OnGuideFuncUnlocked);

            RegisterEvent(EventID.OnAdCounted, OnAdCounted);
            RegisterEvent(EventID.OnAdRewarded, OnAdCounted);
            OnAdCounted(0);
        }

        void OnAdCounted(int key, params object[] args)
        {
            m_BtnAdReward.gameObject.SetActive(PlayerInfoMgr.data.totalAdCount > 0);

            var id = 1;
            for (int i = 0; i < TDAdRewardConfTable.count; i++)
            {
                if (!PlayerInfoMgr.data.lstRewardedAdRewardIds.Contains(TDAdRewardConfTable.dataList[i].id))
                {
                    id = TDAdRewardConfTable.dataList[i].id;
                    break;
                }
            }

            if (PlayerInfoMgr.data.lstRewardedAdRewardIds.Count == TDAdRewardConfTable.count)
            {
                m_ImgAdFill.fillAmount = 1f;
            }
            else if (id > 0)
            {
                var lastConf = TDAdRewardConfTable.GetData(id - 1);
                if (lastConf == null)
                    m_ImgAdFill.fillAmount = PlayerInfoMgr.data.totalAdCount * 1f / TDAdRewardConfTable.GetData(id).needCount;
                else
                    m_ImgAdFill.fillAmount = (PlayerInfoMgr.data.totalAdCount - lastConf.needCount) * 1f /
                                             (TDAdRewardConfTable.GetData(id).needCount - lastConf.needCount);
            }
            else
            {
                m_ImgAdFill.fillAmount =  PlayerInfoMgr.data.totalAdCount * 1f / TDAdRewardConfTable.dataList[0].needCount;
            }
        }

        // void OnPlayerInfoUpdate(int key, params object[] para)
        // {
        //     m_PlayerInfo.UpdateExpAnim(m_TransFlyExp, 0);
        // }
        void OnGuideFuncUnlocked(int key, params object[] para)
        {
            TryOpenTopBlock();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            TryOpenTopBlock();

            UpdateCurChapterInfo(WorldInfoMgr.data.battleData.lstChapters[WorldInfoMgr.data.battleData.lstChapters.Count - 1]);

            // m_AnimBtnAcieve.closeAnim = !GuideMgr.S.IsGuideFinish(Define.GUIDE_ACHIEVEMENT_ID);
            // m_AnimBtnPass.closeAnim = !GuideMgr.S.IsGuideFinish(Define.GUIDE_PASSPORT_ID);

            ShowSubUiAnim();
            m_ViewRewrdBox.SetDataCount(WorldInfoMgr.data.battleData.lstChapters.Count * 3);
            UpdateRewardBoxPos();

            var items = GetComponentsInChildren<GamingPropertyItem>();
            for (int i = 0; i < items.Length; i++)
            {
                items[i].InitFlyTrans();
            }

            m_UnlockFuncTips.Open();

            OpenDependPanel(UIID.TaskMainPanel, -1);
        }

        void TryOpenTopBlock()
        {
            int unlockFunId = PlayerPrefs.GetInt("UnlockChapterId", 0);
            bool showUnlockEntrance = false;
            if (unlockFunId < TDFuncUnlockTable.count)
            {
                var conf = TDFuncUnlockTable.dataList[unlockFunId];
                if (FunctionUnlockHelper.IsChapterUnlock(conf.unlockChapter, conf.unlockWave))
                {
                    UIMgr.S.OpenPanel(UIID.TopBlockPanel);
                    PlayerPrefs.SetInt("UnlockChapterId", unlockFunId + 1);
                    showUnlockEntrance = true;
                    UIMgr.S.OpenPanel(UIID.GuideEntranceUnlockPanel, conf);
                }
            }

            if (!showUnlockEntrance)
            {
                if (UIMgr.S.FindPanel(UIID.TopBlockPanel) != null)
                {
                    UIMgr.S.FindPanel(UIID.TopBlockPanel).CloseSelfPanel();
                }
                // m_PlayerInfo.UpdateExpAnim(m_TransFlyExp);

                int lastLvl,lvl = -1;
                if (IsChapterUnlockNewHeroLvl(out lastLvl, out lvl))
                    UIMgr.S.OpenPanel(UIID.HeroLvlLimitUpPanel, lastLvl, lvl);
            }
        }

        void UpdateRewardBoxPos()
        {
            for (int i = 0; i < WorldInfoMgr.data.battleData.lstChapters.Count; i++)
            {
                int minId = WorldInfoMgr.data.battleData.lstChapters[i].GetMinAvaiableRewardId();
                if (minId >= 0)
                {
                    m_ViewRewrdBox.Jump2Index(i * 3 + minId);
                    return;
                }
            }

            m_ViewRewrdBox.Jump2Index(WorldInfoMgr.data.battleData.lstChapters.Count * 3 - 1);
        }

        void UpdateCurChapterInfo(ChapterItemData item)
        {
            m_CurSelectData = item;
            m_BtnLeft.gameObject.SetObjActive(WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId - 1) != null);
            m_BtnRight.gameObject.SetObjActive(WorldInfoMgr.data.battleData.GetChapterItem(m_CurSelectData.chapterId + 1) != null);
            var conf = TDBattleChapterConfTable.GetData(m_CurSelectData.chapterId);
            m_TxtChapter.text = $"{TDLanguageTable.Get("Stage")} {conf.id}";
            m_TxtChapterTitle.text = TDLanguageTable.Get(conf.chapterName);
            m_TxtChapterWaveMax.text = $"{TDLanguageTable.Get("GamingPanel_Wave")} {m_CurSelectData.maxWave}";
            FindSpriteAsync(conf.chapterIcon, (s) =>
            {
                m_ImgIcon.sprite = s;
                m_ImgIcon.SetNativeSizeWidthFit(564);
            }, true);
            if (string.IsNullOrEmpty(conf.chapterSpine))
            {
                m_ChapterSkeleton.skeletonDataAsset = null;
            }
            else
            {
                AddressableResMgr.S.LoadAssetAsyncByName<SkeletonDataAsset>(conf.chapterSpine, (res, state) =>
                {
                    if (state)
                    {
                        m_ChapterSkeleton.skeletonDataAsset = res;
                        m_ChapterSkeleton.Initialize(true);
                    }
                });
            }

            m_UnlockHeroLvl.UpdateLvlInfo(item);
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        bool IsChapterUnlockNewHeroLvl(out int lastLvl, out int lvl)
        {
            lastLvl = lvl = -1;
            var data = WorldInfoMgr.data.battleData.GetMaxWinChapter();

            var lastData = WorldInfoMgr.data.battleData.GetChapterItem(data.chapterId - 1);
            var conf = TDBattleChapterConfTable.GetData(data.chapterId);
            if (lastData != null)
            {
                var lastConf = TDBattleChapterConfTable.GetData(lastData.chapterId);
                if (lastConf != null && conf != null)
                {
                    if (conf.heroLvLimit > lastConf.heroLvLimit && 
                        !WorldInfoMgr.data.heroData.lstHeroLvlLimitRecords.Contains(conf.id))
                    {
                        lastLvl = lastConf.heroLvLimit;
                        lvl = conf.heroLvLimit;
                        WorldInfoMgr.data.heroData.RecordHeroLvlLimitUp(conf.id);
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }

            return false;
        }
    }
}