using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;

namespace GameWish.Game
{
    public class BattleStagePanel : AbstractAnimPanel
    {
        [SerializeField] private But<PERSON> m_BtnPause;
        [SerializeField] private <PERSON><PERSON> m_BtnEnemy;
        [SerializeField] private Button m_BtnBook;
        [SerializeField] private Button m_BtnNewFunc;
        [SerializeField] private Text m_TxtStageId;
        [SerializeField] private Text m_TxtWave;
        [SerializeField] private Button m_BtnStart;
        [SerializeField] private Button m_BtnAdWood;
        [SerializeField] private Text m_TxtAdWood;
        [SerializeField] private Button m_BtnGather;
        [SerializeField] private Button m_BtnFreeCoin;
        [SerializeField] private Text m_TxtFreeCoinTime;
        // [SerializeField] private Button m_BtnFreeHero;
        [SerializeField] private Text m_TxtFreeHeroTime;
        [SerializeField] private Image m_ImgFreeHeroIcon;
        [SerializeField] private Slider m_BattleBuffEXPSlider;

        [SerializeField] private Animator m_Animator;     
        
        [SerializeField] private RogueSkillCDGroup m_RogueSkillCDGroup;  
        [SerializeField] private BattleBuffGroupCount m_BuffGroup;  
        
        [SerializeField] private Transform m_TrsCoin; 
        [SerializeField] private Transform m_TrsGem; 
        [SerializeField] private Transform m_TrsGear; 
        [SerializeField] private Transform m_TrsBag; 
        
        private int m_TimerFreeCoin;
        private int m_TimerFreeHero;

        public static Transform trsCoin;
        public static Transform trsGem;
        public static Transform trsGear;
        public static Transform trsBag;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnPause.onClick.AddListener(OnClickPause);
            m_BtnStart.onClick.AddListener(OnClickBattle);
            m_BtnAdWood.onClick.AddListener(OnClickAdWood);
            m_BtnGather.onClick.AddListener(OnClickGater);
            m_BtnNewFunc.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.FuncUnlockPanel); });
            m_BtnEnemy.gameObject.SetActive(false);
            // m_BtnEnemy.onClick.AddListener(() => { UIMgr.S.OpenPanel(UIID.EnemyInfoPanel); });

            m_BattleBuffEXPSlider.gameObject.SetActive(false);
            m_RogueSkillCDGroup.gameObject.SetActive(false);
            m_BuffGroup.gameObject.SetActive(false);
            
            // m_RogueSkillCDGroup.DoInit(this);
            m_BuffGroup.DoInit(this);

            trsCoin = m_TrsCoin.transform;
            trsGem = m_TrsGem.transform;
            trsGear = m_TrsGear.transform;
            trsBag = m_TrsBag.transform;
        }


        void OnClickPause()
        {
            UIMgr.S.OpenPanel(UIID.BattlePausePanel);
        }

        void OnClickBattle()
        {
            // if (AppConfig.S.isGuideActive && !GuideMgr.S.IsGuideFinish(Define.GUIDE_JOYSTICK))
            //     return;

            if (!WorldInfoMgr.data.buildingData.IsMainHallUnlock())
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("MainLandIsLock"));
                return;
            }

            // m_BtnBook.gameObject.SetActive(false);
            // m_BtnNewFunc.gameObject.SetActive(false);
            m_BtnStart.gameObject.SetActive(false);
            CloseDependPanel(UIID.TaskMainPanel);
            EventSystem.S.Send(EventID.HideBottomBar);
            m_Animator.Play("BattleStart");
            m_BtnGather.gameObject.SetActive(false);
            m_BattleBuffEXPSlider.gameObject.SetActive(true);

            // m_RogueSkillCDGroup.gameObject.SetActive(true);
            // m_RogueSkillCDGroup.LoadSkill();
            m_BuffGroup.gameObject.SetActive(true);
            m_BuffGroup.LoadBuff();
            
            BattleGroundMgr.S.StartBattle();
        }

        void OnClickGater()
        {
            EventSystem.S.Send(EventID.OnGather);
        }

        void OnClickAdWood()
        {
            AdsPlayMgr.S.PlayRewardAd("GetBattleStartWood", (c) =>
            {
                var reward = RewardInfo.Allocate();
                reward.rewardType = ItemTypeEnum.Wood;
                reward.rewardAmount = ChapterMgr.S.GetLastWaveConf().adWoodReward;

                UIMgr.S.OpenPanel(UIID.RewardPanel, new List<RewardInfo>() { reward });
                BattleGroundMgr.S.SetAddWoodInterval();
                m_BtnAdWood.gameObject.SetActive(false);
            }, null, m_BtnAdWood.transform.position);
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            RegisterEvent(EngineEventID.OnGuideStepFinish, OnGuideStepFinish);
            RegisterEvent(EventID.OnBattleWaveEnd, OnBattleWaveEnd);
            RegisterEvent(EventID.OnChapterLoaded, OnChapterLoaded);
            RegisterEvent(EventID.OnBattleStartHide, OnBattleStartHide);
            RegisterEvent(EventID.OnBattleRestart, OnBattleRestart);
            RegisterEvent(EventID.OnBuildingUpgradeIdSet, OnBuildingUnlock);

            RegisterEvent(EventID.OnStartDoubleCoin, OnStartDoubleCoin);

            if (AppConfig.S.isGuideActive)
            {
                m_BtnStart.gameObject.SetActive(WorldInfoMgr.data.buildingData.IsMainHallUnlock());
                m_BtnGather.gameObject.SetActive(WorldInfoMgr.data.buildingData.IsMainHallUnlock());
            }

            FindSpriteAsync(TDHeroConfTable.GetData(Define.GUIDE_FREEHERO).icon, (spr) =>
            {
                m_ImgFreeHeroIcon.sprite = spr;
                m_ImgFreeHeroIcon.SetNativeSizeWidthFit(140);
            }, true);

            // m_RogueSkillCDGroup.LoadSkill();
            m_BuffGroup.LoadBuff();
        }
        void OnStartDoubleCoin(int key, params object[] para)
        {
            Timer.S.Cancel(m_TimerFreeCoin);
            int leftTime = PlayerInfoMgr.data.guideData.GetLeftDoubleCoinTime();
            if (leftTime > 0)
            {
                m_BtnFreeCoin.gameObject.SetObjActive(true);
                m_TxtFreeCoinTime.text = DateFormatHelper.FormatTime(leftTime, true);
                m_TimerFreeCoin = Timer.S.Post2Really((c) =>
                    {
                        m_TxtFreeCoinTime.text = DateFormatHelper.FormatTime(leftTime - c, true);
                        if (c >= leftTime)
                        {
                            OnStartDoubleCoin(0);
                        }
                    }, 1, leftTime);
            }
            else
            {
                m_BtnFreeCoin.gameObject.SetObjActive(false);
            }
        }
        
        void OnGuideStepFinish(int key, params object[] args)
        {
            if (args != null && args.Length > 0)
            {
                var guideStepId = (int)args[0];
                switch (guideStepId)
                {
                    case Define.GUIDE_STEP_UNLOCK_LOGGING:
                    case Define.GUIDE_STEP_UNLOCK_ARROWTOWER:
                        m_BtnStart.gameObject.SetActive(true);
                        // EventSystem.S.Send(EventID.ShowBottomBar);
                        m_Animator.Play("BattleEnd");
                        // m_RogueSkillCDGroup.gameObject.SetActive(false);
                        m_BuffGroup.gameObject.SetActive(false);
                        CheckGatherStatus();
                        EventSystem.S.Send(EngineEventID.OnPanelUpdate);
                        break;
                }
            }
        }

        void OnChapterLoaded(int key, params object[] para)
        {
        }

        void OnBattleStartHide(int key, params object[] para)
        {
            if (AppConfig.S.isGuideActive)
            {
                m_BtnStart.gameObject.SetActive(false);
                CloseDependPanel(UIID.TaskMainPanel);
                m_BtnGather.gameObject.SetActive(false);
                m_BattleBuffEXPSlider.gameObject.SetActive(false);
                EventSystem.S.Send(EventID.HideBottomBar);
                // m_RogueSkillCDGroup.gameObject.SetActive(true);
                m_BuffGroup.gameObject.SetActive(true);
                m_Animator.Play("BattleStart");
            }
        }

        void OnBattleRestart(int key, params object[] para)
        {
            m_BtnStart.gameObject.SetActive(true);
            OpenDependPanel(UIID.TaskMainPanel, -1);
            // EventSystem.S.Send(EventID.ShowBottomBar);
            m_Animator.Play("BattleEnd");
            // m_RogueSkillCDGroup.gameObject.SetActive(false);
            m_BuffGroup.gameObject.SetActive(false);
            CheckGatherStatus();
        }

        void OnBattleWaveEnd(int key, params object[] para)
        {
            int adWoodReward = ChapterMgr.S.GetLastWaveConf().adWoodReward;
#if VIDEO
            adWoodReward = 0;
#endif
            m_BtnAdWood.gameObject.SetActive(adWoodReward > 0 && BattleGroundMgr.S.CanAddWood());
            m_TxtAdWood.text = $"(5)+{ChapterMgr.S.GetLastWaveConf().adWoodReward}";
            m_BattleBuffEXPSlider.gameObject.SetActive(false);

            if (para != null && para.Length > 0)
            {
                bool isWin = (bool)para[0];
                if (isWin)
                {
                    m_BtnStart.gameObject.SetActive(true);
                    OpenDependPanel(UIID.TaskMainPanel, -1);
                    // EventSystem.S.Send(EventID.ShowBottomBar);
                    m_Animator.Play("BattleEnd");
                    // m_RogueSkillCDGroup.gameObject.SetActive(false);
                    m_BuffGroup.gameObject.SetActive(false);
                    CheckGatherStatus();
                }
            }

            UpdateWave();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            m_TxtStageId.text = $"{TDLanguageTable.Get("Stage").ToUpper()} {ChapterMgr.S.CurStartChapterId}";
            m_BtnStart.gameObject.SetActive(true);
            OpenDependPanel(UIID.TaskMainPanel, -1);
            EventSystem.S.Send(EventID.HideBottomBar);
            m_Animator.Play("BattleEnd");
            // m_RogueSkillCDGroup.gameObject.SetActive(false);
            m_BuffGroup.gameObject.SetActive(false);
            CheckGatherStatus();
            OnBattleWaveEnd(0);

            OnStartDoubleCoin(0);
        }

        void UpdateWave()
        {
            m_TxtWave.text =
                $"{TDLanguageTable.Get("GamingPanel_Wave")} {ChapterMgr.S.CurChapterWave}/{TDBattleWaveRewardConfTable.GetWaveRewardConfs(ChapterMgr.S.CurStartChapterId).Count}";
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }
        protected override void OnClose()
        {
            base.OnClose();
            // m_RogueSkillCDGroup.DoClean();
            m_BuffGroup.DoClean();
            
            Timer.S.Cancel(m_TimerFreeCoin);
            Timer.S.Cancel(m_TimerFreeHero);
        }

        private void Update()
        {
            CheckGatherStatus();
        }

        void CheckGatherStatus()
        {
            if (BattleGroundMgr.S.battleState == BattleStateEnum.Battle)
            {
                m_BtnGather.gameObject.SetActive(false);
                return;
            }

            foreach (var building in BattleGroundMgr.S.dictBattleBuildings.Values)
            {
                if (!building.isUnlock) continue;
                if (building is BarracksCtrller)
                {
                    m_BtnGather.gameObject.SetActive(true);
                    return;
                }
            }

            m_BtnGather.gameObject.SetActive(false);
        }

        void OnBuildingUnlock(int key, params object[] para)
        {
            CheckGatherStatus();
        }
    }
}