using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using PrimeTween;

namespace GameWish.Game
{
    public class HeroEquipPanel_EquipUpgrade : MonoBehaviour
    {
        [SerializeField] private GameObject m_ObjLock;
        [SerializeField] private Text m_TxtUnlockDes;
        [SerializeField] private GameObject m_ObjUnLock;
        [SerializeField] private HeroEquipPanel_EquipItem m_EquipItem;
        [SerializeField] private Text m_TxtName;
        [SerializeField] private Text m_TxtVal;
        [SerializeField] private Button m_BtnUpgrade;
        [SerializeField] private Text m_TxtUpCost;
        [SerializeField] private Button m_BtnAdv;
        [SerializeField] private Text m_TxtAdvCost;
        [SerializeField] private Transform m_TransAdvTips;
        private EquipPartEnum m_PartEnum;
        private int m_UnlockLv;
        private EquipmentSlotInfoRecorder m_SlotData;
        private void OnEnable()
        {
            m_BtnUpgrade.onClick.AddListener(DoClickUpgrade);
            m_BtnAdv.onClick.AddListener(DoClickUpgrade);
            EventSystem.S.Register(EventID.OnEquipmentSlotUpgrade, OnEquipmentSlotUpgrade);
            EventSystem.S.Register(EventID.OnPackItemStack, OnPackItemStack);
            UpdateState();
            UpdateInfo(false);
            UpdateUpColor();
        }
        private void OnDisable()
        {
            m_BtnUpgrade.onClick.RemoveAllListeners();
            m_BtnAdv.onClick.RemoveAllListeners();
            EventSystem.S.UnRegister(EventID.OnEquipmentSlotUpgrade, OnEquipmentSlotUpgrade);
            EventSystem.S.UnRegister(EventID.OnPackItemStack, OnPackItemStack);
        }
        void DoClickUpgrade()
        {
            if (m_SlotData.CanUpgrade())
            {
                AudioMgr.S.PlaySound(AudioID.AUDIO_UPGRADEGEAR);
                m_SlotData.UpgradeLevel();
            }
            else
            {
                AudioMgr.S.PlaySoundSingleton(AudioID.AUDIO_BUTTON_CLICK, true);
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }
        void OnEquipmentSlotUpgrade(int key, params object[] para)
        {
            int part = (int)para[0];
            if (part == (int)m_PartEnum)
            {
                UpdateInfo(true);
                UpdateUpColor();
            }
        }
        void OnPackItemStack(int key, params object[] para)
        {
            if (para != null && para.Length > 2)
            {
                if ((int)para[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum)para[1];
                    if (type == ItemTypeEnum.Coin || type == ItemTypeEnum.EquipAdvStone)
                    {
                        UpdateUpColor();
                    }
                }
            }
        }
        void UpdateInfo(bool withAnim)
        {
            if (m_SlotData.IsLevelMax())
            {
                m_TransAdvTips.localScale = Vector3.zero;
                m_BtnUpgrade.gameObject.SetObjActive(false);
                m_BtnAdv.gameObject.SetObjActive(false);
                m_TxtVal.text = $"{HeroUIUtils.GetEquipPropName(m_SlotData.GetUpPropType())}: {HeroUIUtils.GetEquipPropVal(m_SlotData.GetUpPropType(), m_SlotData.GetCurVal())}";
            }
            else
            {
                if (m_SlotData.GetUpCostType() == ItemTypeEnum.Coin)
                {
                    m_TransAdvTips.localScale = Vector3.zero;
                    m_BtnUpgrade.gameObject.SetObjActive(true);
                    m_BtnAdv.gameObject.SetObjActive(false);
                    m_TxtUpCost.text = $"({(int)m_SlotData.GetUpCostType()}){LongToShortHelper.Long2ShortStr(m_SlotData.GetUpCostVal())}";
                }
                else
                {
                    m_TransAdvTips.localScale = Vector3.one;
                    m_BtnUpgrade.gameObject.SetObjActive(false);
                    m_BtnAdv.gameObject.SetObjActive(true);
                    m_TxtAdvCost.text = $"({(int)m_SlotData.GetUpCostType()}){m_SlotData.GetUpCostVal()}";
                }
                m_TxtVal.text = $"{HeroUIUtils.GetEquipPropName(m_SlotData.GetUpPropType())}: {HeroUIUtils.GetEquipPropVal(m_SlotData.GetUpPropType(), m_SlotData.GetCurVal())}  (icon)  <color=#1cc808>{HeroUIUtils.GetEquipPropVal(m_SlotData.GetUpPropType(), m_SlotData.GetNextVal())}</color>";
            }

            if (withAnim)
            {
                m_TxtVal.transform.DOKill(true);
                m_TxtVal.transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
            }
        }
        public void Init(AbstractPanel panel, int index)
        {
            m_PartEnum = Define.EQUIP_PART_SORTINGS[index];
            m_UnlockLv = Define.EQUIP_PART_UNLOCKLV[index];
            m_SlotData = WorldInfoMgr.data.equipmentData.slotsData.GetEquipedData(m_PartEnum);
            m_EquipItem.Init(panel, m_PartEnum);
            m_TxtUnlockDes.text = string.Format(TDLanguageTable.Get("UnlockEquipDes"), m_UnlockLv);
            m_TxtName.text = TDLanguageTable.Get(TDEquipmentConfTable.GetData(m_SlotData.weaponId).name);
            UpdateUpColor();
        }
        void UpdateState()
        {
            var chapter = WorldInfoMgr.data.battleData.GetChapterItem(m_UnlockLv);
            bool isUnlock = m_UnlockLv == 0 || (chapter != null && chapter.isWin);
            m_ObjLock.SetObjActive(!isUnlock);
            m_ObjUnLock.SetObjActive(isUnlock);
        }

        void UpdateUpColor()
        {
            m_TxtAdvCost.color = m_SlotData.CanUpgrade()
                ? Color.white
                : Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
            m_TxtUpCost.color = m_TxtAdvCost.color;
        }
    }
}
