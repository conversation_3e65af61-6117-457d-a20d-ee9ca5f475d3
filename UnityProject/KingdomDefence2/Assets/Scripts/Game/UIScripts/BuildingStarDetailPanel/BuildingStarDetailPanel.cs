using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using PrimeTween;
using UnityEngine.EventSystems;

namespace GameWish.Game
{
    public class BuildingStarDetailPanel : AbstractAnimPanel, IPointerDownHandler
    {
        [SerializeField] private Button m_BtnClose;
        [SerializeField] private ParticleSystem m_PsEff;
        
        //info
        [SerializeField] private Text m_TxtLevel;
        [SerializeField] private Text m_TxtName;

        [SerializeField] private Image m_ImgIconShadow;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private Image m_ImgIconDeco;

        [SerializeField] private BuildingStarDetailAttrGroup m_BaseGroupAttr;
        [SerializeField] private BuildingStarDetailAttrGroup m_BarracksGroupAttr;

        //btn
        [SerializeField] private Button m_BtnLevelUp;
        [SerializeField] private Button m_BtnAdLevelUp;

        //cost
        [SerializeField] private GameObject m_ObjCostPiece;
        [SerializeField] private GameObject m_ObjCostGear;
        [SerializeField] private Text m_TxtGearCost;
        [SerializeField] private Image m_ImgPieceCost;
        [SerializeField] private Text m_TxtPieceCost;

        //ability
        [SerializeField] private GameObject m_ObjSkillAbilityRoot;
        [SerializeField] private GameObject m_ObjNormalAbilityRoot;

        [SerializeField] private BuildingStarDetialSkillItem m_SkillItemBase;
        [SerializeField] private BuildingStarDetialAbilityItem m_SkillAbilityItemBase;
        [SerializeField] private BuildingStarDetialAbilityItem m_NormalAbilityItemBase;

        [SerializeField] private BuildingStarDetailTip m_SkillTip = null;

        private List<BuildingStarDetialSkillItem> m_SkillItems = new();
        private List<BuildingStarDetialAbilityItem> m_SkillAbilityItems = new();
        private List<BuildingStarDetialAbilityItem> m_NormalAbilityItems = new();

        private BuidlingStarInfoRecorder m_Data;
        private BattleBuildingRTData m_RtDataTmp;

        private string m_LastAbilities;

        protected override void OnUIInit()
        {
            base.OnUIInit();

            m_BtnLevelUp.onClick.AddListener(ClickLevelUp);
            m_BtnAdLevelUp.onClick.AddListener(ClickFreeLevelUp);
            m_BtnClose.onClick.AddListener(HideSelfWithAnim);

            m_SkillItemBase.gameObject.SetActive(false);
            m_SkillAbilityItemBase.gameObject.SetActive(false);
            m_NormalAbilityItemBase.gameObject.SetActive(false);
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            OpenDependPanel(EngineUI.MaskPanel, -1);

            RegisterEvent(EventID.OnBuildingUpgradeStar, OnBuildingUpgradeStar);
            RegisterEvent(EventID.OnBuildingUpgradeLevelGlobal, OnBuildingUpgradeLevelGlobal);
            RegisterEvent(EventID.OnClickBuildingBattleBuffItem, OnClickBuildingBattleBuffItem);
            HideBuildingTip();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);

            int upgradeId = (int)args[0];
            m_RtDataTmp = new BattleBuildingRTData(upgradeId, null);
            m_Data = WorldInfoMgr.data.buildingData.GetBuildingStarData(upgradeId);

            m_TxtName.text = TDLanguageTable.Get(m_Data.GetInfoConf().name);

            //icon
            if (!string.IsNullOrEmpty(m_Data.GetInfoConf().icon))
            {
                AddressableResMgr.S.LoadAssetAsyncByName<Sprite>(m_Data.GetInfoConf().icon, (sprite, state) =>
                {
                    if (state)
                    {
                        m_ImgIcon.sprite = sprite;
                        m_ImgIcon.SetNativeSize();
                    }
                });
            }

            if (!string.IsNullOrEmpty(m_Data.GetInfoConf().iconShadow))
            {
                AddressableResMgr.S.LoadAssetAsyncByName<Sprite>(m_Data.GetInfoConf().iconShadow, (sprite, state) =>
                {
                    if (state)
                    {
                        m_ImgIconShadow.sprite = sprite;
                        m_ImgIconShadow.SetNativeSize();
                    }
                });
            }

            m_ImgIconDeco.gameObject.SetActive(false);
            if (!string.IsNullOrEmpty(m_Data.GetInfoConf().iconDeco))
            {
                AddressableResMgr.S.LoadAssetAsyncByName<Sprite>(m_Data.GetInfoConf().iconDeco, (sprite, state) =>
                {
                    if (state)
                    {
                        m_ImgIconDeco.gameObject.SetActive(true);
                        m_ImgIconDeco.sprite = sprite;
                        m_ImgIconDeco.SetNativeSize();
                    }
                });
            }

            TDItemConfTable.GetItemIcon(m_Data.GetInfoConf().lvlUpCostType, (spr) =>
            {
                m_ImgPieceCost.sprite = spr;
                m_ImgPieceCost.SetNativeSize();
            });

            UpdateInfo();
            UpdateCost();
            UpdateAbility();
        }

        void UpdateCost()
        {
            if (m_Data == null)
                return;

            if (m_Data.IsLevelUpAvaiable())
            {
                m_ObjCostPiece.SetActive(m_Data.GetLevelConf().costPiece > 0);
                m_ObjCostGear.SetActive(m_Data.GetLevelConf().costValue > 0);
                m_TxtPieceCost.text =
                    $"{PackMgr.S.GetPlayerPackItemCurrent(m_Data.GetInfoConf().lvlUpCostType)}/{m_Data.GetLevelConf().costPiece}";
                m_TxtGearCost.text =
                    $"{PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)m_Data.GetLevelConf().costType)}/{m_Data.GetUpgradeLevelCost()}";
                // m_TxtLevelUpCost.text = $"({type}){PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)type)}/{m_Data.GetUpgradeLevelCost()}";
                m_BtnLevelUp.gameObject.SetActive(true);
                m_BtnAdLevelUp.gameObject.SetActive(false);
            }
            else
            {
                if (m_Data.IsLevelMax())
                {
                    m_ObjCostPiece.SetActive(false);
                    m_ObjCostGear.SetActive(false);

                    m_BtnLevelUp.gameObject.SetActive(false);
                    m_BtnAdLevelUp.gameObject.SetActive(false);
                }
                else
                {
                    m_ObjCostPiece.SetActive(m_Data.GetLevelConf().costPiece > 0);
                    m_ObjCostGear.SetActive(m_Data.GetLevelConf().costValue > 0);
                    m_TxtPieceCost.text = m_Data.IsLackOfPieceVal()
                        ? $"<color=#{Define.RES_NOTENOUGH_COLOR}>{PackMgr.S.GetPlayerPackItemCurrent(m_Data.GetInfoConf().lvlUpCostType)}</color>/{m_Data.GetLevelConf().costPiece}"
                        : $"{PackMgr.S.GetPlayerPackItemCurrent(m_Data.GetInfoConf().lvlUpCostType)}/{m_Data.GetLevelConf().costPiece}";
                    m_TxtGearCost.text = m_Data.IsLackOfTypeVal()
                        ? $"<color=#{Define.RES_NOTENOUGH_COLOR}>{PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)m_Data.GetLevelConf().costType)}</color>/{m_Data.GetUpgradeLevelCost()}"
                        : $"{PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)m_Data.GetLevelConf().costType)}/{m_Data.GetUpgradeLevelCost()}";

                    // m_TxtLevelUpCost.text =
                    //     $"({type})<color=#{Define.RES_NOTENOUGH_COLOR}>{PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)type)}</color>/{m_Data.GetUpgradeLevelCost()}";

                    if (HeroMgr.S.IsHeroFreeLvlUpOpen())
                    {
                        m_BtnLevelUp.gameObject.SetActive(false);
                        m_BtnAdLevelUp.gameObject.SetActive(true);
                    }
                    else
                    {
                        m_BtnLevelUp.gameObject.SetActive(true);
                        m_BtnAdLevelUp.gameObject.SetActive(false);
                    }
                }
            }
        }

        void UpdateInfo()
        {
            if (m_Data == null)
                return;

            m_TxtLevel.text = string.Format("Lv.{0}", m_Data.level);
            m_RtDataTmp.LoadConfData();
            int curAtk = (int)m_RtDataTmp.currentAtk;
            int curHp = (int)m_RtDataTmp.currentHp;
            bool isLevelMax = m_Data.IsLevelMax();

            // Log.e(curAtk);

            // int nextAtk = 0;
            // int nextHp = 0;
            // if (!isLevelMax)
            // {
            //     m_RtDataTmp.LoadConfData(m_Data.level + 1);
            //     nextAtk = (int)m_RtDataTmp.currentAtk;
            //     nextHp = (int)m_RtDataTmp.currentHp;
            // }

            //attr
            m_BarracksGroupAttr.gameObject.SetActive(false);
            m_BaseGroupAttr.gameObject.SetActive(false);

            switch (m_Data.GetInfoConf().TypeBuilding)
            {
                case BuildingType.Barrack:
                    m_BarracksGroupAttr.gameObject.SetActive(true);
                    m_BarracksGroupAttr.DoInit();
                    m_BarracksGroupAttr.SetMainHp(curHp);

                    var function =
                        HomeMgr.S.GetBuildingFunctionConfigSO(m_Data.GetStarConf().functionConfig).functionConfigs[0].function as
                            BuildingFunctionConfig_SummonSolider;
                    if (function != null)
                    {
                        var soldierData = new SoldierBattleRTData(null,
                            TDSoldierConfTable.GetData(function.soldierId), 1);
                        m_BarracksGroupAttr.SetSubAtk(soldierData.currentAtk);
                        m_BarracksGroupAttr.SetSubHp(soldierData.maxHp);
                    }

                    m_BarracksGroupAttr.SetDesc(TDLanguageTable.Get(m_Data.GetInfoConf().desc));
                    break;
                case BuildingType.DefenceWall:
                case BuildingType.Logging:
                    m_BaseGroupAttr.gameObject.SetActive(true);
                    m_BaseGroupAttr.DoInit();
                    m_BaseGroupAttr.SetMainHp(curHp);

                    m_BaseGroupAttr.SetDesc(TDLanguageTable.Get(m_Data.GetInfoConf().desc));
                    break;
                default:
                    m_BaseGroupAttr.gameObject.SetActive(true);
                    m_BaseGroupAttr.DoInit();
                    m_BaseGroupAttr.SetMainAtk(curAtk);
                    m_BaseGroupAttr.SetMainHp(curHp);

                    m_BaseGroupAttr.SetDesc(TDLanguageTable.Get(m_Data.GetInfoConf().desc));
                    break;
            }
        }

        void UpdateAbility()
        {
            if (m_Data == null)
                return;


            var hasSkill = m_Data.GetStarConf().lstUIAllBuffIds.Count > 0;
            m_ObjSkillAbilityRoot.SetActive(hasSkill);
            m_ObjNormalAbilityRoot.SetActive(!hasSkill);


            if (hasSkill)
            {
                if (string.IsNullOrEmpty(m_LastAbilities))
                    m_LastAbilities = m_Data.GetStarConf().uIUnlockBuffs;
                else
                {
                    if (m_LastAbilities == m_Data.GetStarConf().uIUnlockBuffs)
                        return;
                    m_LastAbilities = m_Data.GetStarConf().uIUnlockBuffs;
                }

                ClearSkillItems();

                for (int i = 0; i < m_Data.GetStarConf().lstUIUnlockBuffIds.Count; i++)
                {
                    var item = GameObject.Instantiate(m_SkillItemBase);
                    item.transform.SetParent(m_SkillItemBase.transform.parent);
                    item.gameObject.SetActive(true);
                    item.transform.ResetTrans();
                    item.SetInfo(this, m_Data.GetStarConf().lstUIUnlockBuffIds[i], i);
                    m_SkillItems.Add(item);
                }

                for (int i = 0; i < m_Data.GetStarConf().lstUIAllBuffIds.Count; i++)
                {
                    var item = GameObject.Instantiate(m_SkillAbilityItemBase);
                    item.transform.SetParent(m_SkillAbilityItemBase.transform.parent);
                    item.gameObject.SetActive(true);
                    item.transform.ResetTrans();
                    item.SetInfo(this, m_Data.GetStarConf().lstUIAllBuffIds[i], i,
                        m_Data.GetStarConf().lstUIUnlockBuffIds.Contains(m_Data.GetStarConf().lstUIAllBuffIds[i]));
                    m_SkillAbilityItems.Add(item);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(m_LastAbilities))
                    m_LastAbilities = m_Data.GetStarConf().desc;
                else
                {
                    if (m_LastAbilities == m_Data.GetStarConf().desc)
                        return;
                    m_LastAbilities = m_Data.GetStarConf().desc;
                }

                ClearNormalAbilityItems();

                var index = 0;
                foreach (var conf in TDBuildingUpgradeConfTable.GetUpgradeConfs(m_Data.index).Values)
                {
                    if (string.IsNullOrEmpty(conf.desc))
                        continue;
                    var item = GameObject.Instantiate(m_NormalAbilityItemBase);
                    item.transform.SetParent(m_NormalAbilityItemBase.transform.parent);
                    item.gameObject.SetActive(true);
                    item.transform.ResetTrans();
                    item.SetInfo(this, index++, m_Data.level, m_Data.GetInfoConf(), conf);
                    m_NormalAbilityItems.Add(item);
                }
            }
        }

        void UpdateSuperAttr()
        {
            if (m_Data == null)
                return;

            // m_TxtStarUp.color = m_Data.IsStarUpAvaiable() ? Color.white : Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
            // m_StarItemPrefab.SetData(m_Data);
            // var starCount = TDBuildingUpgradeConfTable.GetUpgradeConfs(m_Data.index).Count;
            // for (int i = 0; i < m_LstSuperAttr.Count; i++)
            // {
            //     m_LstSuperAttr[i].SetInfo(i, m_Data.level, TDBuildingUpgradeConfTable.GetUpgradeConf(m_Data.index, i + 2));
            // }
        }

        void ClearSkillItems()
        {
            for (int i = 0; i < m_SkillItems.Count; i++)
            {
                GameObject.Destroy(m_SkillItems[i].gameObject);
            }

            for (int i = 0; i < m_SkillAbilityItems.Count; i++)
            {
                GameObject.Destroy(m_SkillAbilityItems[i].gameObject);
            }

            m_SkillItems.Clear();
            m_SkillAbilityItems.Clear();
        }

        void ClearNormalAbilityItems()
        {
            for (int i = 0; i < m_NormalAbilityItems.Count; i++)
            {
                GameObject.Destroy(m_NormalAbilityItems[i].gameObject);
            }

            m_NormalAbilityItems.Clear();
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
            m_LastAbilities = null;
        }

        protected override void OnClose()
        {
            base.OnClose();
            ClearNormalAbilityItems();
            ClearSkillItems();
            m_SkillTip.transform.DOKill();
            m_Data = null;
            m_RtDataTmp = null;
        }

        public override BackKeyCodeResult OnBackKeyDown()
        {
            HideSelfWithAnim();
            return BackKeyCodeResult.PROCESS_AND_BLOCK;
        }


        void OnBuildingUpgradeStar(int key, params object[] para)
        {
            // m_LstSuperAttr[m_Data.star - 1].transform.DOKill(true);
            // m_LstSuperAttr[m_Data.star - 1].transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
            // for (int i = 0; i < m_LstBasicAttr.Count; i++)
            // {
            //     m_LstBasicAttr[i].transform.DOKill(true);
            //     m_LstBasicAttr[i].transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
            // }

            UpdateInfo();
            UpdateCost();
        }

        void OnBuildingUpgradeLevelGlobal(int key, params object[] para)
        {
            // m_StarItemPrefab.transform.DOKill(true);
            // m_StarItemPrefab.transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
            UpdateInfo();
            UpdateCost();
            UpdateAbility();
        }

        void OnClickBuildingBattleBuffItem(int key, params object[] args)
        {
            var id = (int)args[0];
            var index = (int)args[1];

            var conf = TDBattleBuffConfTable.GetData(id);
            if (conf != null)
            {
                m_SkillTip.transform.localScale = Vector3.zero;
                m_SkillTip.transform.DOKill();
                m_SkillTip.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutBack);
                m_SkillTip.gameObject.SetActive(true);
                m_SkillTip.gameObject.transform.position = m_SkillItems[index].transform.position;
                m_SkillTip.SetData(this, conf);
            }
        }

        public void HideBuildingTip()
        {
            m_SkillTip.gameObject.SetActive(false);
        }

        public void OnPointerDown(PointerEventData evt)
        {
            if (m_SkillTip.gameObject.activeInHierarchy)
                HideBuildingTip();
        }

        void ClickLevelUp()
        {
            if (m_Data.IsLevelUpAvaiable())
            {
                m_Data.DoUpgradeLevel();

                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("building", m_Data.index)
                    .AddEventParam("level", m_Data.level)
                    .AddEventParam("free", false)
                    .CustomEventDic(DataAnalysisID.EVENT_UPGRADE_BUILDING, DAPE.ThinkingData);
                if (m_PsEff != null)
                {
                    m_PsEff.gameObject.SetActive(true);
                    m_PsEff.Play();
                }
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        void ClickFreeLevelUp()
        {
            if (m_Data.IsLevelUpAvaiable(true))
            {
                Action a = DoFreeLevelUp;
                UIMgr.S.OpenPanel(UIID.ConfirmUpgradePanel, a);
            }
        }


        void DoFreeLevelUp()
        {
            AdsPlayMgr.S.PlayRewardAd("FreeBuildingLvlUp", (clicked) =>
            {
                HeroMgr.S.RecordHeroFreeLvlUp();
                m_Data.DoUpgradeLevel(true);

                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("building", m_Data.index)
                    .AddEventParam("level", m_Data.level)
                    .AddEventParam("free", true)
                    .CustomEventDic(DataAnalysisID.EVENT_UPGRADE_BUILDING, DAPE.ThinkingData);
                if (m_PsEff != null)
                {
                    m_PsEff.gameObject.SetActive(true);
                    m_PsEff.Play();
                }
            }, null, transform.position);
        }
    }
}