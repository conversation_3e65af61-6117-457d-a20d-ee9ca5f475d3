using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using System;

namespace GameWish.Game
{
    public class ConfirmDropRewardPanel : AbstractAnimPanel
    {
        [SerializeField] private Button m_BtnClose;
        [SerializeField] private Button m_BtnGet;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private Text m_TxtGet;
        [SerializeField] private Text m_TxtGetDouble;
        [SerializeField] private Text m_TxtBtnGet;

        private Action m_Callback;
        private RewardInfo m_Reward;
        private int m_Ratio;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnClose.onClick.AddListener(OnClickGet);
            m_BtnGet.onClick.AddListener(OnClickGet2);

            GameTimeMgr.S.LockGlobalTS();
        }

        protected override void OnOpen()
        {
            base.OnOpen();

            var waveRewardConf = ChapterMgr.S.GetTheLastWaveConf();
            var boxConf = TDDropGemBoxConfTable.GetRandomConfByWeight(1);
            if (waveRewardConf == null || boxConf == null)
            {
                CloseSelfPanel();
                return;
            }

            m_Reward = RewardInfo.Allocate();
            m_Ratio = boxConf.adRatio;

            m_TxtBtnGet.text = m_Ratio == 5? TDLanguageTable.Get("DoubleRewardsGet5") : TDLanguageTable.Get("DoubleRewardsGet2");
            
            var icon = ConfirmUIUtils.GetRewardIconName(boxConf.rewardType);
            if (!string.IsNullOrEmpty(icon))
            {
                FindSpriteAsync(icon, (spr) =>
                {
                    if (spr != null)
                    {
                        m_ImgIcon.sprite = spr;
                        m_ImgIcon.SetNativeSize();
                    }
                }, true);
            }

            switch (boxConf.rewardType)
            {
                case ItemTypeEnum.CraftOrb:
                    m_Reward.rewardAmount = (long)(boxConf.rewardParam * waveRewardConf.buildingGear);
                    m_Reward.flyTrans = BattleStagePanel.trsGear;
                    break;
                case ItemTypeEnum.Coin:
                    m_Reward.rewardAmount = (long)(boxConf.rewardParam * waveRewardConf.gold *
                                                   (1 + TreasureMgr.S.playerTreasureRuntimeData.coinRewardRadio) *
                                                   PlayerInfoMgr.data.guideData.GetFreeDoubleCoinRate());
                    m_Reward.flyTrans = BattleStagePanel.trsCoin;
                    break;
                default:
                    m_Reward.rewardAmount = (int)boxConf.rewardParam;
                    m_Reward.flyTrans = BattleStagePanel.trsGem;
                    break;
            }

            m_Reward.rewardType = boxConf.rewardType;
            m_Reward.src = "dropChest";

            m_Reward.flyStartPosition = m_ImgIcon.transform.position;
            m_TxtGet.text = string.Format("({0}) x{1}", (int)m_Reward.rewardType, m_Reward.rewardAmount);
            m_TxtGetDouble.text = string.Format("({0}) x{1}", (int)m_Reward.rewardType, m_Reward.rewardAmount * m_Ratio);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);

            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("name", "ConfirmDropRewardPanel")
                .CustomEventDic(DataAnalysisID.EVENT_PANEL_OPEN, DAPE.ThinkingData);
        }

        protected override void OnClose()
        {
            base.OnClose();
            GameTimeMgr.S.FreeGlobalTS();
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        public override BackKeyCodeResult OnBackKeyDown()
        {
            OnClickGet();
            return BackKeyCodeResult.PROCESS_AND_BLOCK;
        }

        void OnClickGet()
        {
            m_Reward.GetReward();
            HideSelfWithAnim();
        }

        void OnClickGet2()
        {
            AdsPlayMgr.S.PlayRewardAd("DoubleDropChestReward", (clicked) =>
            {
                m_Reward.rewardAmount *= m_Ratio;
                m_Reward.GetReward();
                HideSelfWithAnim();
            }, null, m_BtnGet.transform.position);
        }
    }
}