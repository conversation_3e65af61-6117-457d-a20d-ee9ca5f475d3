using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using Spine.Unity;

namespace GameWish.Game
{
    public class BattleVictoryPanel : AbstractAnimPanel
    {
        [SerializeField] private SkeletonGraphic m_WinSpineAnim;
        [SerializeField] private Button m_BtnContinue;
        [SerializeField] private Button m_BtnAd;
        [SerializeField] private Text m_TxtRewardBonus;
        [SerializeField] private BaseRewardItem m_RewardItem;
        [SerializeField] private BattleSettlementRewardItem m_RewardItem1;
        [SerializeField] private Transform m_TransRewardRoot;

        [SerializeField] private GameObject m_ObjDropRoot;
        [SerializeField] private Transform m_TransDropRoot;

        private List<BaseRewardItem> m_Items = new();
        private List<RewardInfo> m_LstReward = new List<RewardInfo>();

        protected override void OnUIInit()
        {
            base.OnUIInit();
            PoolingMgr.S.AddPoolingUI("BaseRewardItem", m_RewardItem.gameObject);
            PoolingMgr.S.AddPoolingUI("SettlementRewardItem", m_RewardItem1.gameObject);
            m_TxtRewardBonus.text = string.Format("{0} x2", TDLanguageTable.Get("BattleSettle_BtnReward"));
            m_BtnContinue.onClick.AddListener(() => { GetReward(1); });
            m_BtnAd.onClick.AddListener(() =>
            {
                AdsPlayMgr.S.PlayRewardAd("victoryDouble", (c) => { GetReward(2); }, null, m_BtnAd.transform.position);
            });
            GameTimeMgr.S.LockGlobalTS();
            JoystickMgr.S.LockJoystick("Movement");
        }

        void GetReward(int rate)
        {
            for (int i = 0; i < m_LstReward.Count; i++)
            {
                m_LstReward[i].rewardAmount *= rate;
                m_LstReward[i].GetReward();
            }

            HideSelfWithAnim();

            if (rate <= 1)
            {
                var chapter = WorldInfoMgr.data.battleData.GetChapterItem(2);
                if (chapter != null)
                {
                    AdsPlayMgr.S.PlayInterCommon("NoVictoryDoubleInter", null);
                }
            }
        }

        protected override void OnOpen()
        {
            base.OnOpen();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            UIMgr.S.ClosePanelAsUIID(UIID.GlobalSkillSelectPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.BattleBuffPanel);

            OpenDependPanel(EngineUI.MaskPanel, -1);
            AudioMgr.S.PlaySound(AudioID.AUDIO_BATTLE_WIN);
            m_WinSpineAnim.PlayUIAnim("1", false, () => { m_WinSpineAnim.PlayUIAnim("2", true, null); });
            ChapterMgr.S.CompleteChapter(true);
            UpdateReward();
            ShowSubUiAnim();

            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("chapter", ChapterMgr.S.CurStartChapterId)
                .AddEventParam("wave", ChapterMgr.S.CurChapterWave)
                .AddEventParam("capId", WorldInfoMgr.data.heroData.mainHeroId)
                .AddEventParam("fightTime", (int)ChapterMgr.S.FightTime)
                .AddEventParam("adRestartNum", ChapterMgr.S.FailAdNum)
                .AddEventParam("heroDieNum", ChapterMgr.S.HeroDieNum)
                .CustomEventDic(DataAnalysisID.EVENT_BATTLEWAVE_ENDWIN, DAPE.ThinkingData);

            UIMgr.S.ClosePanelAsUIID(UIID.RogueSkillSelectPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.GlobalSkillSelectPanel);
        }

        void UpdateReward()
        {
            var conf = ChapterMgr.S.GetLastWaveConf();
            var coin = RewardInfo.Allocate();
            coin.rewardType = ItemTypeEnum.Coin;
            coin.rewardAmount = (long)(conf.gold * (1 + TreasureMgr.S.playerTreasureRuntimeData.coinRewardRadio) *
                                       PlayerInfoMgr.data.guideData.GetFreeDoubleCoinRate());
            coin.src = "victory";
            m_LstReward.Add(coin);

            // var exp = RewardInfo.Allocate();
            // exp.rewardType = ItemTypeEnum.Exp;
            // exp.rewardAmount = conf.exp;
            // exp.src = "victory";
            // m_LstReward.Add(exp);

            var coinItem = PoolingMgr.S.GetUIPoolItem<BaseRewardItem>("BaseRewardItem");
            coinItem.gameObject.SetActive(true);
            coinItem.transform.SetParent(m_TransRewardRoot);
            coinItem.transform.ResetTrans();
            coinItem.SetInfo(this, coin);
            m_Items.Add(coinItem);

            // var expItem = PoolingMgr.S.GetUIPoolItem<BaseRewardItem>("BaseRewardItem");
            // expItem.gameObject.SetActive(true);
            // expItem.transform.SetParent(m_TransRewardRoot);
            // expItem.transform.ResetTrans();
            // expItem.SetInfo(this, exp);
            // m_Items.Add(expItem);

            //dropbag
            m_ObjDropRoot.gameObject.SetActive(WorldInfoMgr.data.battleData.dropBagCounter > 0);
            for (int i = 0; i < WorldInfoMgr.data.battleData.dropBagCounter; i++)
            {
                var boxConf = TDDropGemBoxConfTable.GetRandomConfByWeight();
                var info = DropObjMgr.S.DrawSingleReward(boxConf, conf);
                if (info == null)
                    continue;
                info.src = "victory";
                m_LstReward.Add(info);

                var bagItem = PoolingMgr.S.GetUIPoolItem<BattleSettlementRewardItem>("SettlementRewardItem");
                bagItem.gameObject.SetActive(true);
                bagItem.transform.SetParent(m_TransDropRoot);
                bagItem.transform.ResetTrans();
                bagItem.SetInfo(this, info);
                m_Items.Add(bagItem);
            }
        }


        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            foreach (var reward in m_Items)
            {
                reward.FlyReward();
            }

            CloseSelfPanel();
        }

        protected override void OnClose()
        {
            base.OnClose();
            for (int i = 0; i < m_Items.Count; i++)
            {
                PoolingMgr.S.RecycleUIObj(m_Items[i].gameObject);
            }

            m_Items.Clear();
            for (int i = 0; i < m_LstReward.Count; i++)
            {
                m_LstReward[i].Relese();
            }

            m_LstReward.Clear();
            AudioMgr.S.PlayGameBGM(0);
            ChapterMgr.S.ExitChapter();
            JoystickMgr.S.FreeJoystick("Movement");
            GameCamMgr.S.SetFollowPlayer(true);
            GameTimeMgr.S.CleanGameTS();
        }
    }
}