using System.Collections;
using System.Collections.Generic;
using Qarth;
using UnityEngine;
using UnityEngine.UI;

namespace GameWish.Game
{
    public partial class BuildingStarPanel : AbstractAnimPanel
    {
        [Header("Btn")] [SerializeField] private Text m_TextDrawTip;
        [SerializeField] private Text m_TextADCount;
        [SerializeField] private Button m_BtnDrawAD;
        [SerializeField] private Text m_TxtDraw1Price;
        [SerializeField] private Button m_BtnDraw1;
        [SerializeField] private Text m_TxtDraw10Price;
        [SerializeField] private Button m_BtnDraw10;

        protected int m_FreeTimer = -1;
        protected int m_LeftSec;

        private List<BuildingStarItem> m_Items = new();

        private BuildingShopData shopData => BuildingMgr.S.shopData;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            PoolingMgr.S.AddPoolingUI("BuildingStarItem", m_BuildingStarItem.gameObject);
            m_BuildingStarItem.gameObject.SetActive(false);
            InitItems();

            m_BtnDrawAD.onClick.AddListener(OnDrawAD);
            m_BtnDraw1.onClick.AddListener(OnDraw1);
            m_BtnDraw10.onClick.AddListener(OnDraw10);
            m_TxtDraw10Price.text = $"(2) {Define.BUILDING_DRAW_10_COST}";
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            // RegisterEvent(EventID.Back2Stage, (key, args) => { HideSelfWithAnim(); });
            RegisterEvent(EventID.OnCloseIDPanel, (key, args) =>
            {
                if ((int)args[0] == uiID)
                    HideSelfWithAnim();
            });
            RegisterEvent(EventID.OnReceiveBuildingPiece, RefreshSlot);
            RegisterEvent(EventID.OnBuildingUpgradeStar, RefreshSlot);
            RegisterEvent(EventID.OnBuildingUpgradeLevelGlobal, RefreshSlot);
            RegisterEvent(EventID.OnPackItemStack, OnPackItemStack);

            CheckFreeState();
            RefreshDrawTips();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            ShowSubUiAnim();
            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("name", "BuildingStarPanel")
                .CustomEventDic(DataAnalysisID.EVENT_PANEL_OPEN, DAPE.ThinkingData);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Content.rectTransform());
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
            if (m_FreeTimer != -1)
            {
                m_FreeTimer = -1;
                Timer.S.Cancel(m_FreeTimer);
            }
        }

        public override BackKeyCodeResult OnBackKeyDown()
        {
            EventSystem.S.Send(EventID.ResetBottom2Stage);
            // HideSelfWithAnim();
            return BackKeyCodeResult.PROCESS_AND_BLOCK;
        }

        void InitItems()
        {
            void CreateItem(BuidlingStarInfoRecorder data, Transform root)
            {
                var item = PoolingMgr.S.GetUIPoolItem<BuildingStarItem>("BuildingStarItem");
                item.transform.SetParent(root);
                item.transform.ResetTrans();
                item.SetData(data);
                item.gameObject.SetActive(true);
                m_Items.Add(item);
            }

            var towerIds = TDBuildingInfoConfTable.GetTowerShowBuildingIds();
            foreach (var ids in towerIds.Values)
            {
                foreach (var id in ids)
                {
                    var data = WorldInfoMgr.data.buildingData.GetBuildingStarData(id);
                    CreateItem(data, m_ContentTower.root.transform);
                }
            }

            var barrackIds = TDBuildingInfoConfTable.GetBarrackShowBuildingIds();
            foreach (var ids in barrackIds.Values)
            {
                foreach (var id in ids)
                {
                    var data = WorldInfoMgr.data.buildingData.GetBuildingStarData(id);
                    CreateItem(data, m_ContentBarrack.root.transform);
                }
            }

            var otherIds = TDBuildingInfoConfTable.GetOtherShowBuildingIds();
            foreach (var id in otherIds)
            {
                var data = WorldInfoMgr.data.buildingData.GetBuildingStarData(id);
                CreateItem(data, m_ContentOther.root.transform);
            }


            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Content.rectTransform());
        }

        void RefreshSlot(int key = 0, params object[] args)
        {
            for (int i = 0; i < m_Items.Count; i++)
            {
                var item = m_Items[i];
                item.Update();
            }
        }

        void CheckFreeState()
        {
            if (shopData.freeCount < BuildingShopData.MAX_COUNT)
            {
                m_LeftSec = shopData.GetFreeLeftTime();
                if (m_LeftSec <= 0)
                {
                    // m_TxtFree.gameObject.SetActive(false);
                    m_TextADCount.text = $"{BuildingShopData.MAX_COUNT - shopData.freeCount}/{BuildingShopData.MAX_COUNT}";
                }
                else
                {
                    // m_TxtFree.gameObject.SetActive(true);
                    // m_TxtFree.text = $"{Define.DAILY_GEM_PROVIDE_LIMIT - PlayerInfoMgr.data.shoppingData.dailyTotalGemProvideCount}/{Define.DAILY_GEM_PROVIDE_LIMIT}";
                    m_TextADCount.text = DateFormatHelper.FormatTime(m_LeftSec);
                    if (m_FreeTimer != -1)
                        Timer.S.Cancel(m_FreeTimer);
                    m_FreeTimer = Timer.S.Post2Really(OnFreeTimerTick, 1, m_LeftSec);
                }
            }
            else
            {
                m_TextADCount.text = $"{BuildingShopData.MAX_COUNT - shopData.freeCount}/{BuildingShopData.MAX_COUNT}";
            }
        }

        void OnFreeTimerTick(int count)
        {
            m_LeftSec -= 1;
            if (m_LeftSec > 0)
            {
                m_TextADCount.text = DateFormatHelper.FormatTime(shopData.GetFreeLeftTime());
            }
            else
            {
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }
        }

        void OnDrawAD()
        {
            if (shopData.CanFree())
            {
                AdsPlayMgr.S.PlayRewardAd("DrawBuilding", (c) =>
                {
                    shopData.SetFreeTime();
                    // RewardMgr.S.ShowBigReward(false, BuildingMgr.S.GetRandomPiece(1)[0]);
                    UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Building, ItemTypeEnum.Ad, 0, 1);
                    CheckFreeState();
                    RefreshDrawTips();
                    EventSystem.S.Send(EventID.OnUpdateShopFree);
                }, null, m_BtnDrawAD.transform.position);
            }
        }

        void OnDraw1()
        {
            int cost = 1;
            if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= cost)
            {
                PackMgr.S.AddPlayerItem(-cost, ItemTypeEnum.BuildingKey, "buildingDraw1");
                // RewardMgr.S.ShowBigReward(false, BuildingMgr.S.GetRandomPiece(1)[0]);
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Building, ItemTypeEnum.BuildingKey, cost, 1);
                RefreshDrawTips();
            }
            else if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.BUILDING_DRAW_1_COST)
            {
                PackMgr.S.AddPlayerItem(-Define.BUILDING_DRAW_1_COST, ItemTypeEnum.Gem, "buildingDraw1");
                // RewardMgr.S.ShowBigReward(false, BuildingMgr.S.GetRandomPiece(1)[0]);
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Building, ItemTypeEnum.Gem, Define.BUILDING_DRAW_1_COST, 1);
                RefreshDrawTips();
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        void OnDraw10()
        {
            int cost = 10;
            if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= cost)
            {
                PackMgr.S.AddPlayerItem(-cost, ItemTypeEnum.BuildingKey, "buildingDraw1");
                // RewardMgr.S.ShowBigReward(false, BuildingMgr.S.GetRandomPiece(1)[0]);
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Building, ItemTypeEnum.BuildingKey, cost, 10);
                RefreshDrawTips();
            }
            else if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.BUILDING_DRAW_10_COST)
            {
                PackMgr.S.AddPlayerItem(-Define.BUILDING_DRAW_10_COST, ItemTypeEnum.Gem, "buildingDraw10");
                // RewardMgr.S.ShowReward(false, BuildingMgr.S.GetRandomPiece(10));
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Building, ItemTypeEnum.Gem, Define.BUILDING_DRAW_10_COST, 10);
                RefreshDrawTips();
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        void RefreshDrawTips()
        {
            // m_TextDrawTip.text = TDLanguageTable.GetFormat("TreasurePanel_DrawTips", Define.BUIDLING_GUARANTEE - shopData.guaranteeCount);
            m_TxtDraw1Price.color = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.BUILDING_DRAW_1_COST || PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= 1
                ? Color.white
                : Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
            m_TxtDraw10Price.color = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.BUILDING_DRAW_10_COST || PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= 10
                ? Color.white
                : Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
            m_TxtDraw1Price.text = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= 1 ? $"(42) 1" : $"(2) {Define.BUILDING_DRAW_1_COST}";
            m_TxtDraw10Price.text = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.BuildingKey) >= 10 ? $"(42) 10" : $"(2) {Define.BUILDING_DRAW_10_COST}";
        }


        void OnPackItemStack(int key = 0, params object[] para)
        {
            if (para != null && para.Length > 2)
            {
                if ((int)para[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum)para[1];
                    if (type == ItemTypeEnum.BuildingKey || type == ItemTypeEnum.Gem)
                    {
                        RefreshDrawTips();
                    }
                }
            }
        }
    }
}