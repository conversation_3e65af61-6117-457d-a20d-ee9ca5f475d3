using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using DG.Tweening;
using Sequence = DG.Tweening.Sequence;
using Spine;
using Spine.Unity;
using System;
using Lean.Pool;

namespace GameWish.Game
{
    public class WorldTopUIPanel : AbstractPanel
    {
        [SerializeField] private Image m_ImgCoin;
        [SerializeField] private Image m_ImgGem;
        [SerializeField] private Image m_ImgGear;
        [SerializeField] private Image m_ImgTicket;
        [SerializeField] private Image m_ImgHeroCard;
        [SerializeField] private Image m_ImgTaskLiveness;
        [SerializeField] private Image m_ImgPlayerExp;
        [SerializeField] private Image m_ImgGamePower;
        [SerializeField] private Image m_ImgAd;

        [SerializeField] private ElementUIAnimCustomStart m_ImgAdCounter;
        [SerializeField] private Image m_ImgAdCounterBg;
        [SerializeField] private Image m_ImgAdCounterFill;

        [SerializeField] private GameObject m_EffClick;

        private Dictionary<ItemTypeEnum, Transform> m_FlyTransByType;

        private static WorldTopUIPanel m_Panel;

        public static WorldTopUIPanel S
        {
            get { return m_Panel; }
        }

        private GameObjectPoolGroup m_GroupPool;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_FlyTransByType = new Dictionary<ItemTypeEnum, Transform>();
            m_Panel = this;
            m_GroupPool = GameObjectPoolMgr.S.CreatePoolGroup(UIPoolStrategy.S);
            m_GroupPool.AddPool("Coin", m_ImgCoin.gameObject, -1, 0);
            m_GroupPool.AddPool("Gem", m_ImgGem.gameObject, -1, 0);
            m_GroupPool.AddPool("CraftOrb", m_ImgGear.gameObject, -1, 0);
            m_GroupPool.AddPool("HeroCard", m_ImgHeroCard.gameObject, -1, 0);
            m_GroupPool.AddPool(ItemTypeEnum.TaskLivenessExp.ToString(), m_ImgTaskLiveness.gameObject, -1, 0);
            m_GroupPool.AddPool(ItemTypeEnum.Exp.ToString(), m_ImgPlayerExp.gameObject, -1, 0);
            m_GroupPool.AddPool(ItemTypeEnum.GamePower.ToString(), m_ImgGamePower.gameObject, -1, 0);
            m_GroupPool.AddPool(ItemTypeEnum.AdTicket.ToString(), m_ImgTicket.gameObject, -1, 0);
            m_GroupPool.AddPool(ItemTypeEnum.Ad.ToString(), m_ImgAd.gameObject, -1, 0);
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            RegisterEvent(EventID.OnAdCounted, OnAdCounted);
        }

        void Update()
        {
            if (Input.GetMouseButtonDown(0))
            {
                // 判断是否点击在UI元素上
                if (UnityEngine.EventSystems.EventSystem.current.IsPointerOverGameObject())
                {
                    PlayClick(Input.mousePosition);
                }
            }
        }

        public void InitFlyTrans(ItemTypeEnum type, Transform trans)
        {
            m_FlyTransByType[type] = trans;
        }

        public void FlyTransByType(Vector3 start, ItemTypeEnum type)
        {
            if (type == ItemTypeEnum.Gem || type == ItemTypeEnum.Coin || type == ItemTypeEnum.AdTicket)
            {
                if (m_FlyTransByType.ContainsKey(type) && m_FlyTransByType[type] != null)
                {
                    FlyRes(start, m_FlyTransByType[type], type);
                }
            }
        }

        public void PlayClick(Vector3 worldPos)
        {
            var eff = LeanPool.Spawn(m_EffClick);
            eff.gameObject.SetActive(true);
            eff.transform.SetParent(transform);
            eff.transform.localScale = Vector3.one;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(transform as RectTransform, worldPos, null, out Vector2 uiPos);
            eff.transform.localPosition = uiPos;
            // CustomExtensions.ScreenPosition2UIPosition(GameCamMgr.S.mainCam, UIMgr.S.uiRoot.rootCanvas.transform as RectTransform, worldPos, eff.transform);
            eff.AddMissingComponent<ParticleCtrller>();
        }

        public void FlyRes(Vector3 startPos, Transform tarTrs, ItemTypeEnum resType, int count = 20,
            Action onComplete = null)
        {
            if (tarTrs == null)
                return;
            FlyItems(startPos, tarTrs.position, resType, resType.ToString(), count, onComplete);
        }

        void FlyItems(Vector3 srcPos, Vector3 tarPos, ItemTypeEnum resType, string resName, int count = 20,
            Action onComplete = null)
        {
            if (count == 0)
            {
                onComplete?.Invoke();
                return;
            }

            if (m_GroupPool == null)
                return;
            for (int i = 0; i < count; i++)
            {
                var obj = m_GroupPool.Allocate(resName);
                if (obj == null)
                    continue;

                obj.transform.SetParent(transform);
                obj.transform.position = srcPos;
                obj.transform.localScale = Vector3.one;
                float moveTime = Mathf.Abs(tarPos.y - obj.transform.position.y) * 0.1f;
                bool lastOne = (i == count - 1);
                PlaySingleFlyAnim(i, obj.transform, tarPos, moveTime, RandomHelper.Range(0, 360),
                    RandomHelper.Range(60, 90), 0.2f).OnComplete(() =>
                {
                    m_GroupPool.Recycle(obj);
                    EventSystem.S.Send(EventID.OnFlyAnimEnd, resType);
                    if (lastOne)
                    {
                        onComplete?.Invoke();
                    }
                });
            }
        }

        #region FlyAnim

        float m_InitScale = 1f;

        private Sequence PlaySingleFlyAnim(int index, Transform tar, Vector3 tarPos, float moveTime, float angle,
            float r, float idleTime)
        {
            moveTime = Mathf.Clamp(moveTime + RandomHelper.Range(0.07f, 0.12f), 0.1f, 0.5f);
            return DOTween.Sequence()
                .Insert(index * 0.02f, tar.DOScale(m_InitScale, 0.2f).SetEase(Ease.OutBack))
                .Append(tar.DOLocalMove(AutumnMathHelper.GetCirlePosByRadius2D(tar.localPosition, angle, r), 0.15f)
                    .SetEase(Ease.OutBack))
                .Append(tar.DOPunchPosition((Vector3.up * 10), idleTime, 3))
                .Append(tar.DOMove(tarPos, moveTime).SetEase(Ease.InCubic));
        }

        #endregion

        #region UseCashAnim

        public void UseCashUnlock(Vector3 startPos, Vector3 tarPos)
        {
            if (m_GroupPool == null)
                return;
            var obj = m_GroupPool.Allocate("Coin");
            if (obj == null)
                return;

            //将坐标转为UI世界坐标
            Vector3 startScreenPos = GameCamMgr.S.mainCam.WorldToScreenPoint(startPos);
            Vector3 uiStartPos = UIMgr.S.uiRoot.uiCamera.ScreenToWorldPoint(startScreenPos);
            startPos = uiStartPos;
            Vector3 tarScreenPos = GameCamMgr.S.mainCam.WorldToScreenPoint(tarPos);
            Vector3 uiTarPos = UIMgr.S.uiRoot.uiCamera.ScreenToWorldPoint(tarScreenPos);
            tarPos = uiTarPos;

            obj.transform.SetParent(transform);
            obj.transform.position = startPos;
            obj.transform.localScale = Vector3.one * 0.4f;
            PlayUseCashAnim(obj.transform, tarPos).OnComplete(() => { m_GroupPool.Recycle(obj); });
        }

        private Sequence PlayUseCashAnim(Transform tar, Vector3 tarPos)
        {
            return DOTween.Sequence().Append(tar.DOScale(0.8f, 0.2f).SetEase(Ease.OutCubic))
                .Join(tar.DOJump(tarPos, 1, 1, 0.4f).SetEase(Ease.InCubic))
                .Insert(0.2f, tar.DOScale(0.4f, 0.2f).SetEase(Ease.InCubic));
        }

        #endregion

        #region ad

        void OnAdCounted(int key, params object[] args)
        {
            if (PlayerInfoMgr.data.lstRewardedAdRewardIds.Count == TDAdRewardConfTable.count)
                return;

            if (args != null && args.Length > 0)
            {
                var pos = (Vector3)args[0];

                var id = 1;
                for (int i = 0; i < TDAdRewardConfTable.count; i++)
                {
                    if (!PlayerInfoMgr.data.lstRewardedAdRewardIds.Contains(TDAdRewardConfTable.dataList[i].id))
                    {
                        id = TDAdRewardConfTable.dataList[i].id;
                        break;
                    }
                }

                var lastConf = TDAdRewardConfTable.GetData(id - 1);
                if (lastConf == null)
                {
                    m_ImgAdCounterFill.DOKill(true);
                    m_ImgAdCounterFill.fillAmount = (PlayerInfoMgr.data.totalAdCount - 1) * 1f / TDAdRewardConfTable.GetData(id).needCount;
                    m_ImgAdCounterFill.DOFillAmount(PlayerInfoMgr.data.totalAdCount * 1f / TDAdRewardConfTable.GetData(id).needCount,
                        0.3f);
                }
                else
                {
                    m_ImgAdCounterFill.DOKill(true);
                    m_ImgAdCounterFill.fillAmount = (PlayerInfoMgr.data.totalAdCount - lastConf.needCount - 1) * 1f /
                                                    (TDAdRewardConfTable.GetData(id).needCount - lastConf.needCount);
                    m_ImgAdCounterFill.DOFillAmount((PlayerInfoMgr.data.totalAdCount - lastConf.needCount) * 1f /
                                                    (TDAdRewardConfTable.GetData(id).needCount - lastConf.needCount), 0.3f);
                }

                m_ImgAdCounter.ShowAnim(0.2f, () =>
                {
                    FlyRes(pos, m_ImgAdCounter.transform, ItemTypeEnum.Ad, 10, () => { m_ImgAdCounter.HideAnim(); });
                    m_ImgAdCounterBg.DOKill(true);
                    m_ImgAdCounterBg.transform.localScale = Vector3.one;
                    m_ImgAdCounterBg.transform.DOPunchScale(Vector3.one * 0.5f,  0.2f, 5).SetDelay(1.1f);
                });
            }
        }

        #endregion
    }
}