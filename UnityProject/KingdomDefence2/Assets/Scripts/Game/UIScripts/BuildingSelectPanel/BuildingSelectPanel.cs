using System.Collections;
using System.Collections.Generic;
using PrimeTween;
using UnityEngine;
using Qarth;
using QuickEngine.Extensions;

namespace GameWish.Game
{
    public partial class BuildingSelectPanel : AbstractAnimPanel
    {
        private List<BuildingSelectItem> m_Items = new();

        private int m_BuildingId;
        private int m_BuildingType;
        
        

        protected override void OnUIInit()
        {
            base.OnUIInit();
            // m_BtnClose.onClick.AddListener(OnClickClose);
            PoolingMgr.S.AddPoolingUI("BuildingSelectItem", m_BuildingSelectItemPrefab.gameObject);
            m_BuildingSelectItemPrefab.gameObject.SetActive(false);

           
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            RegisterEvent(EventID.OnBuildingUnlock, OnBuildingUnlock);
        }

        void FillContent(Dictionary<int, List<int>> dataMap)
        {
            foreach (var data in dataMap)
            {
                int index = data.Key;
                for (int i = 0; i < data.Value.Count; i++)
                {
                    var item = PoolingMgr.S.GetUIPoolItem<BuildingSelectItem>("BuildingSelectItem");
                    item.transform.name = index.ToString();
                    item.transform.SetParent(m_ContentRoot);
                    item.transform.ResetTrans();
                    item.SetData(this, TDBuildingInfoConfTable.GetData(data.Value[i]), m_BuildingId);
                    item.gameObject.SetActive(true);
                    m_Items.Add(item);
                }
            }
        }
        
        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);
            HideBuildingTip();
            m_BuildingId = (int)args[0];
            m_BuildingType = (int)args[2];
            
            if (m_BuildingType == 2)
            {
                FillContent(TDBuildingInfoConfTable.GetTowerShowBuildingIds());
            }
            else
            {
                FillContent(TDBuildingInfoConfTable.GetBarrackShowBuildingIds());
            }
            ShowSubUiAnim();
        }

        protected override void OnPanelShowComplete()
        {
            // var index = 0;
            //
            // var root = m_TabviewTower.GetTab(index).button;
            // m_SelectRootTower.transform.DOKill();
            // m_SelectRootTower.transform.DOLocalMoveX(root.transform.localPosition.x, 0.1f).SetEase(PrimeTween.Ease.Linear);
            //
            // root = m_TabviewBarrack.GetTab(index).button;
            // m_SelectRootBarrack.transform.DOKill();
            // m_SelectRootBarrack.transform.DOLocalMoveX(root.transform.localPosition.x, 0.1f).SetEase(PrimeTween.Ease.Linear);
        }


        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        protected override void OnClose()
        {
            base.OnClose();
            for (int i = 0; i < m_Items.Count; i++)
            {
                PoolingMgr.S.RecycleUIObj(m_Items[i].gameObject);
            }

            m_Items.Clear();
        }

        void OnBuildingUnlock(int key, params object[] args)
        {
            HideSelfWithAnim();
        }

        public void ShowBuildingTip(BuildingSelectItem item)
        {
            m_BuildingTip.gameObject.SetActive(true);
            var position = item.transform.position + new Vector3(150, 250, 0);
            position.x = Mathf.Clamp(position.x, 400, 700);
            // Debug.LogError(position);
            m_BuildingTip.gameObject.transform.position = position;
            m_BuildingTip.SetData(this, item.conf, item.buildingID);
        }

        public void HideBuildingTip()
        {
            m_BuildingTip.gameObject.SetActive(false);
        }
    }
}