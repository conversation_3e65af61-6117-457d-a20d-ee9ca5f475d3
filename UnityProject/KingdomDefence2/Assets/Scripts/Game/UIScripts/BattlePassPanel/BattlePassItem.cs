using System.Collections;
using System.Collections.Generic;
using Game.Core.UIEffect;
using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using Qarth;

namespace GameWish.Game
{
    public class BattlePassItem : MonoBehaviour
    {
        [SerializeField] private BattlePassRewardItem m_RewardLeft;
        [SerializeField] private BattlePassRewardItem m_RewardRight;
        [SerializeField] private Text m_TxtLevel;
        [SerializeField] private GameObject m_GONot;
        [SerializeField] private GameObject m_HighLightGO;

        public BattlePassRewardItem RewardLeft => m_RewardLeft;

        [Button]
        public void SetStatus(bool isCurrent, bool notYet)
        {
            m_GONot.SetActive(notYet);

            if (m_Conf != null)
            {
                var normalRewardData = PerimeterSystem<BattlePass>.S.Data.normal.GetRewardData(m_Conf.id);
                m_HighLightGO.SetActive(!normalRewardData.hasRewarded && m_Conf.id <= PlayerInfoMgr.data.playerData.level);
            }
        }

        private TDBattlePassConf m_Conf;
        private BattlePassPanel m_Panel;

        public void SetConf(BattlePassPanel panel, TDBattlePassConf conf)
        {
            m_Panel = panel;
            m_Conf = conf;
            m_TxtLevel.text = $"{conf.id}";

            var normalRewardData = PerimeterSystem<BattlePass>.S.Data.normal.GetRewardData(m_Conf.id);
            m_RewardLeft.SetStatus(normalRewardData.hasRewarded);

            var adRewardData = PerimeterSystem<BattlePass>.S.Data.ad.GetRewardData(m_Conf.id);
            m_RewardRight.SetStatus(adRewardData.hasRewarded);

            m_RewardLeft.SetReward(panel, normalRewardData, (ItemTypeEnum)conf.normalRewardType, conf.normalRewardNum);
            m_RewardRight.SetReward(panel, adRewardData, (ItemTypeEnum)conf.advanceRewardType, conf.advanceRewardNum, conf.advanceRewardId);
            m_RewardLeft.bgClickAction = OnClickNormal;
            m_RewardRight.bgClickAction = OnClickAD;

            m_HighLightGO.SetActive(!normalRewardData.hasRewarded && conf.id <= PlayerInfoMgr.data.playerData.level);
        }

        void OnClickNormal(BattlePassRewardItem item)
        {
            item.GetReward();
            item.SetStatus(item.Data.hasRewarded);
            m_HighLightGO.SetActive(!item.Data.hasRewarded);
            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("id", item.Data.id)
                .AddEventParam("isAdv", false)
                .CustomEventDic(DataAnalysisID.EVENT_PASSPORT_REWARD, DAPE.ThinkingData);
        }

        void OnClickAD(BattlePassRewardItem item)
        {
            AdsPlayMgr.S.PlayRewardAd("BattlePass" + item.Data.id, (s) =>
            {
                item.GetReward();
                item.SetStatus(item.Data.hasRewarded);
                DataAnalysisMgr.S.ResetEventMap()
                    .AddEventParam("id", item.Data.id)
                    .AddEventParam("isAdv", true)
                    .CustomEventDic(DataAnalysisID.EVENT_PASSPORT_REWARD, DAPE.ThinkingData);

                m_Panel.UpdateState();
            }, null, item.transform.position);
        }
    }
}