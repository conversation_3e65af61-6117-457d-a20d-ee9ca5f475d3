using System.Collections.Generic;
using Qarth;
using UnityEngine;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class DropBoxPanel : AbstractAnimPanel
    {
        [SerializeField] private Button m_BtnClose;
        [SerializeField] private Button m_BtnGet;
        [SerializeField] private Button m_BtnAgain;

        [SerializeField] private Text m_TxtReward;
        [SerializeField] private Text m_TxtReward3;

        private static int m_RewardRatio = 3;
        private List<RewardInfo> m_LstRewardInfo = new List<RewardInfo>();

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnClose.onClick.AddListener(OnClickClose);
            m_BtnGet.onClick.AddListener(OnClickClose);
            m_BtnAgain.onClick.AddListener(OnClickAgain);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            m_LstRewardInfo.Clear();
            OpenDependPanel(EngineUI.MaskPanel, -1);

            if (args.Length > 1)
            {
                var reward = new RewardInfo();
                reward.rewardType = (ItemTypeEnum)args[0];
                reward.rewardAmount = (int)args[1];

                if (reward.rewardAmount > 0)
                {
                    m_TxtReward.text = string.Format("{0}", reward.rewardAmount);
                    m_TxtReward3.text = string.Format("{0}", reward.rewardAmount * m_RewardRatio);
                    m_LstRewardInfo.Add(reward);
                }
            }
        }

        protected override void OnClose()
        {
            base.OnClose();
            m_LstRewardInfo.Clear();
        }

        protected override void OnPanelHideBegin()
        {
            base.OnPanelHideBegin();
            DropObjMgr.S.HideInteractiveObj();
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        void OnClickClose()
        {
            UIMgr.S.OpenPanel(UIID.RewardPanel, m_LstRewardInfo);
            HideSelfWithAnim();
        }

        void OnClickAgain()
        {
            AdsPlayMgr.S.PlayRewardAd("RewardWood", (clicked) =>
            {
                foreach (var rewardInfo in m_LstRewardInfo)
                {
                    rewardInfo.rewardAmount *= m_RewardRatio;
                }

                UIMgr.S.OpenPanel(UIID.RewardPanel, m_LstRewardInfo);
                HideSelfWithAnim();
            }, null, m_BtnAgain.transform.position);
        }
    }
}