using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;


namespace GameWish.Game
{
    public class BattleFailPanel : AbstractAnimPanel
    {
        [SerializeField] private Text m_TxtFailReward;
        [SerializeField] private Button m_BtnGiveUp;
        [SerializeField] private Button m_BtnReviveAd;
        [SerializeField] private Text m_TxtAdReward;
        private TDBattleWaveRewardConf m_WaveConf;
        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnGiveUp.onClick.AddListener(OnClickGiveUp);
            m_BtnReviveAd.onClick.AddListener(OnClickRevive);

            GameTimeMgr.S.LockGlobalTS();
            JoystickMgr.S.LockJoystick("Movement");
        }
        void OnClickGiveUp()
        {
            UIMgr.S.OpenPanel(UIID.BattleFailRewardPanel);
            HideSelfWithAnim();
        }
        void OnClickRevive()
        {
            AdsPlayMgr.S.PlayRewardAd("battleFailRevive", (c) =>
            {
                var reward = RewardInfo.Allocate();
                reward.rewardType = ItemTypeEnum.Wood;
                reward.rewardAmount = m_WaveConf.loseReward;

                UIMgr.S.OpenPanel(UIID.RewardPanel, new List<RewardInfo>() { reward });
                HomeMgr.S.ReloadWorld(false);
                EventSystem.S.Send(EventID.OnBattleRestart);
                JoystickMgr.S.FreeJoystick("Movement");
                GameCamMgr.S.SetFollowPlayer(true);
                ChapterMgr.S.AddFailAdNum();
                HideSelfWithAnim();
            }, null, m_BtnReviveAd.transform.position);
        }
        protected override void OnOpen()
        {
            base.OnOpen();
        }
        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);
            AudioMgr.S.PlaySound(AudioID.AUDIO_BATTLE_LOSE);

            m_WaveConf = ChapterMgr.S.GetLastWaveConf();
            m_TxtFailReward.text = $"{TDLanguageTable.Get("BattleFailDes")} +{m_WaveConf.loseReward}(5)";
            m_TxtAdReward.text = $"(5)    +{m_WaveConf.loseReward}";

            ShowSubUiAnim();
            
            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("name", "BattleFailPanel")
                .CustomEventDic(DataAnalysisID.EVENT_PANEL_OPEN, DAPE.ThinkingData);
            
            UIMgr.S.ClosePanelAsUIID(UIID.RogueSkillSelectPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.GlobalSkillSelectPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.BattleBuffPanel);
            
            WorldInfoMgr.data.battleData.RecordContinueLevel(-1, -1);
        }
        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }
    }
}
