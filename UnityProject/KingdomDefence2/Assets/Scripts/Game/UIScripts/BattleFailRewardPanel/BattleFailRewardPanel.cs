using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using Spine.Unity;

namespace GameWish.Game
{
    public partial class BattleFailRewardPanel : AbstractAnimPanel
    {
        [SerializeField] private SkeletonGraphic m_FailSpineAnim;
        [SerializeField] private Button m_BtnContinue;
        [SerializeField] private Button m_BtnAd;
        [SerializeField] private Text m_TxtRewardBonus;
        [SerializeField] private BaseRewardItem m_RewardItem;
        [SerializeField] private BattleSettlementRewardItem m_RewardItem1;
        [SerializeField] private Transform m_TransRewardRoot;
        [SerializeField] private GameObject m_ObjDropRoot;
        [SerializeField] private Transform m_TransDropRoot;

        private List<BaseRewardItem> m_Items = new();
        private List<RewardInfo> m_LstReward = new List<RewardInfo>();

        private BattleFailSuggestItem[] m_SuggestItems;

        private BottomBarType m_BottomBarType = BottomBarType.Stage;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            PoolingMgr.S.AddPoolingUI("BaseRewardItem", m_RewardItem.gameObject);
            PoolingMgr.S.AddPoolingUI("SettlementRewardItem", m_RewardItem1.gameObject);
            m_TxtRewardBonus.text = string.Format("{0} x2", TDLanguageTable.Get("BattleSettle_BtnReward"));
            m_BtnContinue.onClick.AddListener(() => { GetReward(1); });
            m_BtnAd.onClick.AddListener(() =>
            {
                AdsPlayMgr.S.PlayRewardAd("failDouble", (c) => { GetReward(2); }, null, m_BtnAd.transform.position);
            });
            m_ButtonHome.onClick.AddListener(HideSelfWithAnim);
            GameTimeMgr.S.LockGlobalTS();
            JoystickMgr.S.LockJoystick("Movement");

            m_SuggestItems = gameObject.GetComponentsInChildren<BattleFailSuggestItem>(true);
            foreach (var item in m_SuggestItems)
            {
                item.Init(this);
            }
        }

        void GetReward(int rate)
        {
            for (int i = 0; i < m_LstReward.Count; i++)
            {
                m_LstReward[i].rewardAmount *= rate;
                m_LstReward[i].GetReward();
            }

            //判断是否有功能解锁
            bool enableSuggest = true;
            if (AppConfig.S.isGuideActive)
            {
                enableSuggest = FunctionUnlockHelper.IsFunctionUnlock(FunctionEnumType.Research) |
                                FunctionUnlockHelper.IsFunctionUnlock(FunctionEnumType.Treasure) |
                                FunctionUnlockHelper.IsFunctionUnlock(FunctionEnumType.Hero);
            }

            if (WorldInfoMgr.data.battleData.GetMaxWinChapterId() < 4)
                enableSuggest = false;

            if (enableSuggest)
            {
                m_Reward.SetActive(false);
                m_Suggest.SetActive(true);
            }
            else
            {
                HideSelfWithAnim();
            }
        }

        protected override void OnOpen()
        {
            base.OnOpen();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);
            m_FailSpineAnim.PlayUIAnim("animation", false, null);
            ChapterMgr.S.CompleteChapter(false);
            UpdateReward();
            ShowSubUiAnim();

            m_BottomBarType = BottomBarType.Stage;
            foreach (var item in m_SuggestItems)
            {
                item.CheckActive();
            }


            m_Reward.SetActive(true);
            m_Suggest.SetActive(false);

            AudioMgr.S.PlaySound(AudioID.AUDIO_CLAIM);
            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("chapter", ChapterMgr.S.CurStartChapterId)
                .AddEventParam("wave", ChapterMgr.S.CurChapterWave)
                .AddEventParam("capId", WorldInfoMgr.data.heroData.mainHeroId)
                .AddEventParam("fightTime", (int)ChapterMgr.S.FightTime)
                .AddEventParam("adRestartNum", ChapterMgr.S.FailAdNum)
                .AddEventParam("heroDieNum", ChapterMgr.S.HeroDieNum)
                .CustomEventDic(DataAnalysisID.EVENT_BATTLEWAVE_ENDLOSE, DAPE.ThinkingData);

            UIMgr.S.ClosePanelAsUIID(UIID.RogueSkillSelectPanel);
            UIMgr.S.ClosePanelAsUIID(UIID.GlobalSkillSelectPanel);
        }

        void UpdateReward()
        {
            var conf = ChapterMgr.S.GetLastWaveConf();
            if (conf.gold > 0)
            {
                var coin = RewardInfo.Allocate();
                coin.rewardType = ItemTypeEnum.Coin;
                coin.rewardAmount = conf.gold * PlayerInfoMgr.data.guideData.GetFreeDoubleCoinRate();
                coin.src = "battleFailReward";
                m_LstReward.Add(coin);

                var coinItem = PoolingMgr.S.GetUIPoolItem<BaseRewardItem>("BaseRewardItem");
                coinItem.gameObject.SetActive(true);
                coinItem.transform.SetParent(m_TransRewardRoot);
                coinItem.transform.ResetTrans();
                coinItem.SetInfo(this, coin);
                m_Items.Add(coinItem);
            }

            if (conf.exp > 0)
            {
                var exp = RewardInfo.Allocate();
                exp.rewardType = ItemTypeEnum.Exp;
                exp.rewardAmount = conf.exp;
                exp.src = "battleFailReward";
                m_LstReward.Add(exp);

                var expItem = PoolingMgr.S.GetUIPoolItem<BaseRewardItem>("BaseRewardItem");
                expItem.gameObject.SetActive(true);
                expItem.transform.SetParent(m_TransRewardRoot);
                expItem.transform.ResetTrans();
                expItem.SetInfo(this, exp);
                m_Items.Add(expItem);
            }


            //dropbag
            m_ObjDropRoot.gameObject.SetActive(WorldInfoMgr.data.battleData.dropBagCounter > 0);
            for (int i = 0; i < WorldInfoMgr.data.battleData.dropBagCounter; i++)
            {
                var boxConf = TDDropGemBoxConfTable.GetRandomConfByWeight();
                var info =  DropObjMgr.S.DrawSingleReward(boxConf, conf);
                if (info == null)
                    continue;
                info.src = "fail";
                m_LstReward.Add(info);

                var bagItem = PoolingMgr.S.GetUIPoolItem<BattleSettlementRewardItem>("SettlementRewardItem");
                bagItem.gameObject.SetActive(true);
                bagItem.transform.SetParent(m_TransDropRoot);
                bagItem.transform.ResetTrans();
                bagItem.SetInfo(this, info);
                m_Items.Add(bagItem);
            }
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
        }

        protected override void OnClose()
        {
            base.OnClose();
            for (int i = 0; i < m_Items.Count; i++)
            {
                PoolingMgr.S.RecycleUIObj(m_Items[i].gameObject);
            }

            m_Items.Clear();
            for (int i = 0; i < m_LstReward.Count; i++)
            {
                m_LstReward[i].Relese();
            }

            m_LstReward.Clear();
            AudioMgr.S.PlayGameBGM(0);
            ChapterMgr.S.ExitChapter();
            JoystickMgr.S.FreeJoystick("Movement");
            GameCamMgr.S.SetFollowPlayer(true);
            GameTimeMgr.S.CleanGameTS();

            Timer.S.Post2Scale((i) =>
            {
                switch (m_BottomBarType)
                {
                    case BottomBarType.Research:
                        EventSystem.S.Send(EventID.OnCallBottomPanel, UIID.BuildingStarPanel);
                        break;
                    case BottomBarType.HeroEquip:
                        EventSystem.S.Send(EventID.OnCallBottomPanel, UIID.HeroEquipPanel);
                        break;
                    case BottomBarType.Treasure:
                        EventSystem.S.Send(EventID.OnCallBottomPanel, UIID.TreasurePanel);
                        break;
                }
            }, 2.0f);
        }

        public void SetButtomBarType(BottomBarType type)
        {
            m_BottomBarType = type;
        }
    }
}