using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using Spine.Unity;
using PrimeTween;

namespace GameWish.Game
{
    public class HeroLvUpPanel : AbstractAnimPanel
    {
        [SerializeField] private ParticleSystem m_PsEff;
        [SerializeField] private ParticleSystem m_PsEffStar;

        [SerializeField] private Button m_BtnLeft;
        [SerializeField] private Button m_BtnRight;


        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private SkeletonGraphic m_HeroSpine;
        [SerializeField] private Text m_TxtName;
        [SerializeField] private Text m_TxtLv;
        [SerializeField] private Text m_TxtAtk;
        [SerializeField] private Text m_TxtHp;

        [SerializeField] private Text m_TxtOwend;

        //skill
        [SerializeField] private Button m_BtnSkill1;
        [SerializeField] private Button m_BtnSkill2;
        [SerializeField] private Button m_BtnRogueSkill1;
        [SerializeField] private Button m_BtnRogueSkill2;
        [SerializeField] private SkillIcon m_ImgSkillIcon1;
        [SerializeField] private SkillIcon m_ImgSkillIcon2;
        [SerializeField] private RogueSkillIconItem m_RogueSkillIcon1;
        [SerializeField] private RogueSkillIconItem m_RogueSkillIcon2;

        //
        [SerializeField] private List<ElementUIAnimCustomStart> m_LstHeroShowAnim;
        private TDHeroConf m_HeroConf;
        private TDSkillConf m_HeroSkillLeader;
        private HeroInfoRecorder m_HeroData;
        private int m_CurAtk;
        private int m_CurHp;
        private float m_CurOwendVal;
        private int m_NextAtk;
        private int m_NextHp;
        private float m_NextOwendVal;
        private HeroBattleRTData m_RtDataTmp;
        private bool m_ShowLvUp;

        //treasure
        [SerializeField] private Image m_ImgTIcon;
        [SerializeField] private Image m_ImgTBG;
        [SerializeField] private Text m_TxtTLvl;
        [SerializeField] private GameObject m_ObjTCanChange;
        [SerializeField] private GameObject m_ObjTCanEquip;

        [SerializeField] private HeroLvlUpPanel_Stars m_StarItem;
        [SerializeField] private Button m_BtnEquipTreasure;

        [SerializeField] private Button m_BtnClose;
        [SerializeField] private Button m_BtnLvUp;
        [SerializeField] private Button m_BtnAdLvUp;
        
        [SerializeField] private Button m_BtnLvlLock;
        [SerializeField] private Text m_TxtLvlLock;
        
        
        [SerializeField] private Button m_BtnStarUp;
        [SerializeField] private Image m_ImgStarPiece;

        [SerializeField] private Text m_TxtLvUpBtn;
        [SerializeField] private Text m_TxtCost;
        [SerializeField] private Text m_TxtCostPiece;
        [SerializeField] private Image m_CostFill;
        [SerializeField] private USimpleListView m_View;
        [SerializeField] private RedpointListener_HeroLvlUp m_ObjUpTips1;
        [SerializeField] private RedpointListener_HeroStarUp m_ObjUpTips2;

        private List<TDHeroUpgradeConf> m_LstUpConfs;
        private int m_Cost;
        private int m_CostCoin;
        private bool m_WithAnim;

        protected override void OnUIInit()
        {
            base.OnUIInit();
            m_BtnClose.onClick.AddListener(HideSelfWithAnim);
            m_View.SetCellRenderer(OnCellRenderer);
            m_BtnLvUp.onClick.AddListener(DoClickUpgradeLvl);
            m_BtnAdLvUp.onClick.AddListener(DoClickFreeUpgradeLvl);
            m_BtnStarUp.onClick.AddListener(DoClickUpgradeStar);
            m_BtnLvlLock.onClick.AddListener(DoClickLockLvl);

            m_BtnSkill1.onClick.AddListener(ClickSkillLeader);
            m_BtnSkill2.onClick.AddListener(ClickSkillAttack);
            m_BtnRogueSkill1.onClick.AddListener(ClickRogueSkill1);
            m_BtnRogueSkill2.onClick.AddListener(ClickRogueSkill2);

            m_BtnLeft.onClick.AddListener(OnClickLeft);
            m_BtnRight.onClick.AddListener(OnClickRight);

            m_BtnEquipTreasure.gameObject.SetActive(false);
            // m_BtnEquipTreasure.onClick.AddListener(OnClickEquipTreasure);
        }

        protected override void OnOpen()
        {
            base.OnOpen();
            foreach (var item in m_LstHeroShowAnim)
            {
                item.ShowAnim();
            }

            RegisterEvent(EventID.OnHeroUpgrade, OnHeroUpgrade);
            RegisterEvent(EventID.OnTreasureEquip, OnTreasureEquip);

            DataAnalysisMgr.S.ResetEventMap()
                .AddEventParam("name", "HeroLvUpPanel")
                .CustomEventDic(DataAnalysisID.EVENT_PANEL_OPEN, DAPE.ThinkingData);
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            int heroId = (int)args[0];

            m_HeroConf = TDHeroConfTable.GetData(heroId);
            m_HeroData = WorldInfoMgr.data.heroData.GetHeroData(m_HeroConf.id);
            m_TxtName.text = TDLanguageTable.Get(m_HeroConf.comment);

            FindSpriteAsync(m_HeroConf.iconPiece, (spr) =>
            {
                m_ImgStarPiece.sprite = spr;
                m_ImgStarPiece.SetNativeSize();
            }, true);

            AddressableResMgr.S.LoadAssetAsyncByName<SkeletonDataAsset>(m_HeroConf.spine, (res, state) =>
            {
                if (state)
                {
                    m_HeroSpine.skeletonDataAsset = res;
                    m_HeroSpine.Initialize(true);
                    m_HeroSpine.PlayUIAnim(m_HeroSpine.SkeletonData.Animations.Items[0].Name, true, null);
                }
            });

            SetHeroInfo();
            LoadTreasure();
            ShowHeroAttribute();

            m_LstUpConfs = TDHeroUpgradeConfTable.GetTalentUpgradeConfs(heroId);
            m_ObjUpTips1.heroId = heroId;
            m_ObjUpTips1.CheckState();
            m_ObjUpTips2.heroId = heroId;
            m_ObjUpTips2.CheckState();
            UpdateInfo();

            EventSystem.S.Send(EventID.OnOpenHeroLvUpPanel, true, heroId);
        }

        void SetHeroInfo()
        {
            m_RtDataTmp = new HeroBattleRTData(HeroTeamMgr.S.mainHero, m_HeroConf);

            var upgradeConf = m_HeroData.GetStarConf();
            var lastUpgradeConf = TDHeroUpgradeConfTable.GetUpgradeConf(m_HeroConf.id, 999);

            if (upgradeConf.lstSkillIds.Count > 0)
            {
                var skillID = upgradeConf.lstSkillIds[0];
                m_HeroSkillLeader = TDSkillConfTable.GetData(skillID);
                if (m_HeroSkillLeader != null)
                {
                    m_ImgSkillIcon1.SetSkill(this, m_HeroSkillLeader);
                }
            }

            m_ImgSkillIcon2.SetSkill(this, m_HeroConf.attackSkillIcon);

            m_BtnRogueSkill1.gameObject.SetActive(lastUpgradeConf.lstRogueSkillIds.Count > 0);
            m_BtnRogueSkill2.gameObject.SetActive(lastUpgradeConf.lstRogueSkillIds.Count > 1);

            for (int i = 0; i < lastUpgradeConf.lstRogueSkillIds.Count; i++)
            {
                if (i == 0)
                    m_RogueSkillIcon1.SetSkill(this, lastUpgradeConf.lstRogueSkillIds[i],
                        upgradeConf.lstRogueSkillIds.Contains(lastUpgradeConf.lstRogueSkillIds[i]));
                if (i == 1)
                    m_RogueSkillIcon2.SetSkill(this, lastUpgradeConf.lstRogueSkillIds[i],
                        upgradeConf.lstRogueSkillIds.Contains(lastUpgradeConf.lstRogueSkillIds[i]));
            }

            OnHeroUpgradeData(0, m_HeroConf.id);
        }


        void ShowHeroAttribute()
        {
            if (m_NextAtk > m_CurAtk)
            {
                m_TxtAtk.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurAtk),
                    LongToShortHelper.Long2ShortStr(m_NextAtk - m_CurAtk));
            }
            else
            {
                m_TxtAtk.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurAtk));
            }

            if (m_NextHp > m_CurHp)
            {
                m_TxtHp.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurHp),
                    LongToShortHelper.Long2ShortStr(m_NextHp - m_CurHp));
            }
            else
            {
                m_TxtHp.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurHp));
            }

            if (m_NextOwendVal > m_CurOwendVal)
            {
                m_TxtOwend.text = string.Format("{0} <color=#36fa49>+{1}</color>", m_CurOwendVal.ToString("P"),
                    (m_NextOwendVal - m_CurOwendVal).ToString("P"));
            }
            else
            {
                m_TxtOwend.text = string.Format("{0}", m_CurOwendVal.ToString("P"));
            }
        }

        void OnClickLeft()
        {
            UIMgr.S.StartCoroutine(HeroMgr.S.ChangeHero(m_HeroConf.id, false, CloseSelfPanel));
        }

        void OnClickRight()
        {
            UIMgr.S.StartCoroutine(HeroMgr.S.ChangeHero(m_HeroConf.id, true, CloseSelfPanel));
        }

        void ClickSkillLeader()
        {
            if (m_HeroSkillLeader != null)
            {
                UIMgr.S.OpenPanel(UIID.SkillInfoDetailPanel, m_BtnSkill1.transform.position, m_HeroSkillLeader.comment,
                    m_HeroSkillLeader.desc);
            }
        }

        void ClickSkillAttack()
        {
            if (m_HeroConf != null)
            {
                UIMgr.S.OpenPanel(UIID.SkillInfoDetailPanel, m_BtnSkill2.transform.position, m_HeroConf.attackSkillName,
                    m_HeroConf.attackSkillDes);
            }
        }

        void ClickRogueSkill1()
        {
            if (m_HeroData.GetStarConf().lstRogueSkillIds.Count > 0)
            {
                var skillConf = TDRogueSkillConfTable.GetData(m_HeroData.GetStarConf().lstRogueSkillIds[0]);
                if (skillConf != null)
                    UIMgr.S.OpenPanel(UIID.SkillInfoDetailPanel, m_BtnRogueSkill1.transform.position, skillConf.name,
                        skillConf.desc);
            }
        }

        void ClickRogueSkill2()
        {
            if (m_HeroData.GetStarConf().lstRogueSkillIds.Count > 1)
            {
                var skillConf = TDRogueSkillConfTable.GetData(m_HeroData.GetStarConf().lstRogueSkillIds[1]);
                if (skillConf != null)
                    UIMgr.S.OpenPanel(UIID.SkillInfoDetailPanel, m_BtnRogueSkill2.transform.position, skillConf.name,
                        skillConf.desc);
            }
        }

        void DoClickUpgradeLvl()
        {
            if (m_HeroData.CanUpgradeLevel())
            {
                PackMgr.S.AddPlayerItem(-m_CostCoin,
                    (ItemTypeEnum)m_HeroData.GetLevelConf().upgradeCostType,
                    "upgradeHero");
                m_HeroData.UpgradeLevel();
                AudioMgr.S.PlaySound(AudioID.AUDIO_LEVELUP);
                if (m_PsEff != null)
                {
                    m_PsEff.gameObject.SetActive(true);
                    m_PsEff.Play();
                }
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        void DoClickFreeUpgradeLvl()
        {
            if (m_HeroData.CanUpgradeLevel(true))
            {
                Action a = DoFreeUpgradeLvl;
                UIMgr.S.OpenPanel(UIID.ConfirmUpgradePanel, a);
            }
        }

        void DoClickLockLvl()
        {
            FloatMessage.S.ShowMsg(TDLanguageTable.Get("Hero_max_lvl_reached"));
        }

        void DoFreeUpgradeLvl()
        {
            AdsPlayMgr.S.PlayRewardAd("FreeHeroLvlUp", (clicked) =>
            {
                HeroMgr.S.RecordHeroFreeLvlUp();
                m_HeroData.UpgradeLevel();
                AudioMgr.S.PlaySound(AudioID.AUDIO_LEVELUP);
                if (m_PsEff != null)
                {
                    m_PsEff.gameObject.SetActive(true);
                    m_PsEff.Play();
                }
            }, null, transform.position);
        }

        void DoClickUpgradeStar()
        {
            if (m_HeroData.CanUpgradeStar())
            {
                m_HeroData.UpgradeStar(m_Cost);
                AudioMgr.S.PlaySound(AudioID.AUDIO_LEVELUP);
                if (m_PsEffStar != null)
                {
                    m_PsEffStar.gameObject.SetActive(true);
                    m_PsEffStar.Play();
                }
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        void LoadTreasure()
        {
            if (m_HeroData == null)
            {
                m_ObjTCanChange.SetActive(false);
                m_ObjTCanEquip.SetActive(false);
                return;
            }

            var treasureData = WorldInfoMgr.data.equipmentData.heroEquipTreasureData.GetHeroEquipData(m_HeroData.id);
            if (treasureData.treasureId == -1)
            {
                m_ObjTCanChange.SetActive(false);
                m_ImgTBG.gameObject.SetActive(false);
                m_ImgTIcon.gameObject.SetActive(false);

                m_ObjTCanEquip.SetActive(WorldInfoMgr.data.treasureData.slotData.GetUnlockTreasureCount() > 0);
                return;
            }

            m_ObjTCanEquip.SetActive(false);
            m_ObjTCanChange.SetActive(true);
            m_ImgTBG.gameObject.SetActive(true);
            m_ImgTIcon.gameObject.SetActive(true);

            var treasureConf = TDTreasureConfTable.GetData(treasureData.treasureId);
            FindSpriteAsync(treasureConf.icon, (spr) =>
            {
                m_ImgTIcon.sprite = spr;
                m_ImgTIcon.SetNativeSize();
            }, true);
            FindSpriteAsync(HeroUIUtils.GetTreasureSelectBGName(treasureConf.quality), (spr) => { m_ImgTBG.sprite = spr; }, true);
            m_TxtTLvl.text = $"LV.{WorldInfoMgr.data.treasureData.slotData.GetTreasureData(treasureData.treasureId).level}";
        }

        void OnClickEquipTreasure()
        {
            if (m_HeroData == null)
                return;
            UIMgr.S.OpenPanel(UIID.TreasureSelectPanel, m_HeroData.id);
        }

        void OnCellRenderer(Transform root, int index)
        {
            root.GetComponent<HeroLvUpPanel_Item>().SetInfo(m_HeroData, m_LstUpConfs[index], m_WithAnim);
        }

        void OnHeroUpgrade(int key, params object[] para)
        {
            m_WithAnim = true;
            UpdateInfo();
            OnHeroUpgradeData(0, m_HeroConf.id);
            ShowHeroAttribute();
            m_WithAnim = false;
        }

        void OnTreasureEquip(int key, params object[] para)
        {
            LoadTreasure();
        }

        void UpdateInfo()
        {
            m_View.SetDataCount(m_LstUpConfs.Count);

            //star
            if (m_HeroData.IsStarMax())
            {
                m_BtnStarUp.gameObject.SetObjActive(false);
                m_TxtCostPiece.text = "MAX";
                m_CostFill.fillAmount = 1f;
            }
            else
            {
                m_BtnStarUp.gameObject.SetObjActive(true);
                m_Cost = TDHeroUpgradeConfTable.GetUpgradeConf(m_HeroData.id, m_HeroData.star).upgradeCost;
                if (m_Cost > 0)
                    m_CostFill.fillAmount = m_HeroData.piece * 1f / m_Cost;
                else
                    m_CostFill.fillAmount = 1f;

                if (m_HeroData.CanUpgradeStar())
                {
                    m_TxtLvUpBtn.color = Color.white;
                    m_TxtCostPiece.text = $"{m_HeroData.piece}/{m_Cost}";
                }
                else
                {
                    m_TxtLvUpBtn.color = Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
                    m_TxtCostPiece.text = $"<color=#{Define.RES_NOTENOUGH_COLOR}>{m_HeroData.piece}</color>/{m_Cost}";
                }
            }

            //level
            if (m_HeroData.IsLevelMax())
            {
                m_BtnLvUp.gameObject.SetObjActive(false);
                m_BtnAdLvUp.gameObject.SetObjActive(false);
                m_BtnLvlLock.gameObject.SetActive(false);
                m_TxtCost.text = null;
            }
            else
            {
                if (m_HeroData.IsLevelLimited())
                {
                    m_BtnLvUp.gameObject.SetObjActive(false);
                    m_BtnAdLvUp.gameObject.SetObjActive(false);
                    m_BtnLvlLock.gameObject.SetActive(true);
                    m_TxtLvlLock.text = TDLanguageTable.Get("HeroLvlLimited") + m_HeroData.GetLimitLevel();
                }
                else
                {
                    m_BtnLvlLock.gameObject.SetActive(false);
                    m_CostCoin = (int)m_HeroData.GetLevelConf().upgradeCostVal;

                    var upgradeType = m_HeroData.GetLevelConf().upgradeCostType;
                    long cur = PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)upgradeType);
                    if (cur >= m_CostCoin)
                    {
                        m_TxtLvUpBtn.color = Color.white;
                        m_TxtCost.text = $"({upgradeType}){cur}/{m_CostCoin}";

                        m_BtnLvUp.gameObject.SetActive(true);
                        m_BtnAdLvUp.gameObject.SetActive(false);
                    }
                    else
                    {
                        m_TxtLvUpBtn.color = Color.white;
                        // m_TxtLvUpBtn.color = Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
                        m_TxtCost.text = $"({upgradeType})<color=#{Define.RES_NOTENOUGH_COLOR}>{cur}</color>/{m_CostCoin}";

                        if (HeroMgr.S.IsHeroFreeLvlUpOpen())
                        {
                            m_BtnLvUp.gameObject.SetActive(false);
                            m_BtnAdLvUp.gameObject.SetActive(true);
                        }
                        else
                        {
                            m_BtnLvUp.gameObject.SetActive(true);
                            m_BtnAdLvUp.gameObject.SetActive(false);
                        }
                    }
                }
            }
        }

        protected override void OnClose()
        {
            base.OnClose();
            UIMgr.S.ClosePanelAsUIID(UIID.ConfirmUpgradePanel);
        }

        protected override void OnPanelHideComplete()
        {
            base.OnPanelHideComplete();
            CloseSelfPanel();
            EventSystem.S.Send(EventID.OnOpenHeroLvUpPanel, false);
        }

        public override BackKeyCodeResult OnBackKeyDown()
        {
            HideSelfWithAnim();
            return BackKeyCodeResult.PROCESS_AND_BLOCK;
        }


        void OnHeroUpgradeData(int key, params object[] para)
        {
            int heroId = (int)para[0];
            if (heroId == m_HeroData.id && m_RtDataTmp != null)
            {
                m_RtDataTmp.LoadConfData();

                m_CurAtk = (int)m_RtDataTmp.currentAtk;
                m_CurHp = (int)m_RtDataTmp.maxHp;
                m_CurOwendVal = TDHeroUpgradeConfTable.GetUpgradeConf(heroId, m_HeroData.star).ownedHp;
                m_RtDataTmp.LoadConfData(m_HeroData.star + 1, m_HeroData.level);
                m_NextAtk = (int)m_RtDataTmp.currentAtk;
                m_NextHp = (int)m_RtDataTmp.maxHp;
                m_NextOwendVal = TDHeroUpgradeConfTable.GetUpgradeConf(heroId, m_HeroData.star + 1).ownedHp;

                m_TxtLv.text = $"Lv.{m_HeroData.level}";
                m_StarItem.SetStar(m_HeroData.star);

                if (m_ShowLvUp)
                {
                    if (m_NextAtk > m_CurAtk)
                    {
                        m_TxtAtk.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurAtk),
                            LongToShortHelper.Long2ShortStr(m_NextAtk - m_CurAtk));
                    }
                    else
                    {
                        m_TxtAtk.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurAtk));
                    }

                    if (m_NextHp > m_CurHp)
                    {
                        m_TxtHp.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurHp),
                            LongToShortHelper.Long2ShortStr(m_NextHp - m_CurHp));
                    }
                    else
                    {
                        m_TxtHp.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurHp));
                    }

                    if (m_NextOwendVal > m_CurOwendVal)
                    {
                        m_TxtOwend.text = string.Format("{0} <color=#36fa49>+{1}</color>", m_CurOwendVal.ToString("P"),
                            (m_NextOwendVal - m_CurOwendVal).ToString("P"));
                    }
                    else
                    {
                        m_TxtOwend.text = string.Format("{0}", m_CurOwendVal.ToString("P"));
                    }

                    m_TxtAtk.transform.DOKill(true);
                    m_TxtHp.transform.DOKill(true);
                    m_TxtOwend.transform.DOKill(true);
                    m_TxtAtk.transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
                    m_TxtHp.transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
                    m_TxtOwend.transform.DOPunchScale(Vector2.one * 0.2f, 0.3f, 3);
                }
                else
                {
                    m_TxtHp.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurHp));
                    m_TxtAtk.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurAtk));
                    m_TxtOwend.text = string.Format("{0}", m_CurOwendVal.ToString("P"));
                }
            }
        }


        // void OnOpenHeroLvUpPanel(int key, params object[] para)
        // {
        //     m_ShowLvUp = (bool)para[0];
        //     if (m_ShowLvUp)
        //     {
        //         int selectHero = (int)para[1];
        //         if (selectHero == m_HeroConf.id)
        //         {
        //             if (m_NextAtk > m_CurAtk)
        //             {
        //                 m_TxtAtk.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurAtk), LongToShortHelper.Long2ShortStr(m_NextAtk - m_CurAtk));
        //             }
        //             else
        //             {
        //                 m_TxtAtk.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurAtk));
        //             }
        //             if (m_NextHp > m_CurHp)
        //             {
        //                 m_TxtHp.text = string.Format("{0} <color=#36fa49>+{1}</color>", LongToShortHelper.Long2ShortStr(m_CurHp), LongToShortHelper.Long2ShortStr(m_NextHp - m_CurHp));
        //             }
        //             else
        //             {
        //                 m_TxtHp.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurHp));
        //             }
        //             if (m_NextOwendVal > m_CurOwendVal)
        //             {
        //                 m_TxtOwend.text = string.Format("{0} <color=#36fa49>+{1}</color>", m_CurOwendVal.ToString("P"), (m_NextOwendVal - m_CurOwendVal).ToString("P"));
        //             }
        //             else
        //             {
        //                 m_TxtOwend.text = string.Format("{0}", m_CurOwendVal.ToString("P"));
        //             }
        //         }
        //     }
        //     else
        //     {
        //         m_TxtHp.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurHp));
        //         m_TxtAtk.text = string.Format("{0}", LongToShortHelper.Long2ShortStr(m_CurAtk));
        //         m_TxtOwend.text = string.Format("{0}", m_CurOwendVal.ToString("P"));
        //     }
        // }
    }
}