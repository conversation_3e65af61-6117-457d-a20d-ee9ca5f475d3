using System;
using Qarth;
using UnityEngine;
using UnityEngine.UI;
using PrimeTween;

namespace GameWish.Game
{
    public class RedpointListener : MonoBehaviour
    {
        [SerializeField] protected string[] m_EvtIDs;
        protected Image m_ImgRedpoint;
        private void Awake()
        {
            if (m_ImgRedpoint == null)
                m_ImgRedpoint = GetComponentInChildren<Image>(true);
        }

        private void OnDestroy()
        {
            if (m_ImgRedpoint != null)
                Tween.StopAll(m_ImgRedpoint.transform);
        }

        protected virtual void OnEnable()
        {
            if (m_EvtIDs == null || m_EvtIDs.Length == 0)
                return;
            for (int i = 0; i < m_EvtIDs.Length; i++)
            {
                EventSystem.S.Register(CustomExtensions.GetStringEnum<EventID>(m_EvtIDs[i]), CheckState);
            }

            CheckState();
        }

        private void OnDisable()
        {
            if (m_EvtIDs == null || m_EvtIDs.Length == 0)
                return;
            for (int i = 0; i < m_EvtIDs.Length; i++)
            {
                EventSystem.S.UnRegister(CustomExtensions.GetStringEnum<EventID>(m_EvtIDs[i]), CheckState);
            }
        }

        public virtual bool IsReady()
        {
            return false;
        }

        public virtual void CheckState(int key = 0, params object[] args)
        {
            if (m_ImgRedpoint == null) m_ImgRedpoint = GetComponentInChildren<Image>(true);
            if (m_ImgRedpoint == null)
            {
                gameObject.SetActive(false);
                return;
            }


            bool ready = IsReady();
            m_ImgRedpoint.gameObject.SetActive(ready);
            if (ready)
            {          
                m_ImgRedpoint.transform.DOKill();  
                m_ImgRedpoint.transform.localPosition = Vector3.zero;
                Tween.LocalPositionY(m_ImgRedpoint.transform, 10, 0.7f, Ease.Linear, -1, CycleMode.Yoyo);
            }
            else
            {
                m_ImgRedpoint.DOKill();
                m_ImgRedpoint.transform.localPosition = Vector3.zero;
            }
        }

        public virtual void Hide(int key = 0, params object[] args)
        {
            m_ImgRedpoint.transform.DOKill();
            m_ImgRedpoint.gameObject.SetActive(false);
        }
    }
}