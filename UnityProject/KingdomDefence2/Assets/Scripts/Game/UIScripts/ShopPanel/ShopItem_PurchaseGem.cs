using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class ShopItem_PurchaseGem : ShopItem_Purchase
    {
        [SerializeField] protected bool m_CanFree;
        [SerializeField] protected Button m_BtnFree;
        [SerializeField] protected Text m_TxtBtnFree;
        [SerializeField] protected Text m_TxtFree;
        [SerializeField] private GameObject m_ObjMask;

        protected int m_FreeTimer = -1;
        protected int m_LeftSec;


        public override void OnInit(AbstractPanel panel)
        {
            base.OnInit(panel);
            if (m_TxtReward != null)
                m_TxtReward.text = string.Format("x{0}", m_Conf.rewardAmount);
            if (m_TxtBtnFree != null)
                m_TxtBtnFree.text = TDLanguageTable.Get("EquipmentShop_Free");

            // EventSystem.S.Register(EventID.OnMgrTicketAdd, UpdateInfo);
            // EventSystem.S.Register(EventID.OnMgrUpgrade, UpdateInfo);
        }

        public override void OnOpen()
        {
            base.OnOpen();
            if (m_BtnFree != null)
                m_BtnFree.onClick.AddListener(OnClickFree);

            EventSystem.S.Register(EventID.OnPackItemStack, OnPackItemStack);

            UpdateInfo(0);
            CheckFreeState();
        }

        public override void OnClose()
        {
            base.OnClose();
            if (m_BtnFree != null)
                m_BtnFree.onClick.RemoveAllListeners();
            EventSystem.S.UnRegister(EventID.OnPackItemStack, OnPackItemStack);
            // EventSystem.S.UnRegister(EventID.OnMgrTicketAdd, UpdateInfo);
            // EventSystem.S.UnRegister(EventID.OnMgrUpgrade, UpdateInfo);
        }
        void OnPackItemStack(int key, params object[] args)
        {
            if (args != null && args.Length > 2)
            {
                if ((int)args[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum)args[1];
                    if (type == ItemTypeEnum.AdTicket)
                    {
                        CheckFreeState();
                    }
                }
            }
        }
        void UpdateInfo(int key, params object[] args)
        {
            if (m_Conf == null)
                return;

            if (m_TxtReward != null)
                m_TxtReward.text = string.Format("x{0}", m_Conf.rewardAmount);
        }

        protected void OnClickFree()
        {
            if (m_Conf == null)
                return;

            if (!PlayerInfoMgr.data.shoppingData.firstShopFreeGem)
            {
                IapRewardMgr.S.GetReward(m_Conf.productId, true, true);
                PlayerInfoMgr.data.shoppingData.SetFreeGemTime();
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }
            else
            {
                AdsPlayMgr.S.PlayRewardAd("FreeGem", (clicked) =>
                {
                    IapRewardMgr.S.GetReward(m_Conf.productId, true, true);
                    PlayerInfoMgr.data.shoppingData.SetFreeGemTime();
                    CheckFreeState();
                    EventSystem.S.Send(EventID.OnUpdateShopFree);
                }, null, m_BtnFree.transform.position);
            }
        }

        protected void CheckFreeState()
        {
            if (m_BtnFree == null || m_TxtFree == null)
                return;

            if (m_CanFree)
            {
                //是否是首次免费
                if (!PlayerInfoMgr.data.shoppingData.firstShopFreeGem)
                {
                    if (m_ObjMask)
                    {
                        m_ObjMask.SetActive(false);
                    }
                    m_TxtFree.gameObject.SetActive(true);
                    m_TxtFree.text = TDLanguageTable.Get("resRoot_FirstFree");
                    m_TxtBtnFree.text = TDLanguageTable.Get("EquipmentShop_Free");
                    m_BtnFree.gameObject.SetActive(true);
                    m_BtnPurchase.gameObject.SetActive(false);
                }
                //判断免费次数是否已经被耗尽
                else if (PlayerInfoMgr.data.shoppingData.dailyTotalGemProvideCount < Define.DAILY_GEM_PROVIDE_LIMIT)
                {
                    if (m_ObjMask)
                    {
                        m_ObjMask.SetActive(false);
                    }
                    m_LeftSec = PlayerInfoMgr.data.shoppingData.GetShopFreeBonusLeftTime();
                    m_BtnFree.gameObject.SetActive(true);
                    m_BtnFree.interactable = m_LeftSec <= 0;
                    m_BtnPurchase.gameObject.SetActive(false);
                    if (m_LeftSec <= 0)
                    {
                        m_TxtFree.gameObject.SetActive(false);
                        m_TxtBtnFree.text = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdTicket) > 0 ? string.Format("(t) {0}", TDLanguageTable.Get("EquipmentShop_Free")) : string.Format("(ad) {0}", TDLanguageTable.Get("EquipmentShop_Free"));
                        m_TxtBtnFree.color = Color.white;
                    }
                    else
                    {
                        m_TxtFree.gameObject.SetActive(true);
                        m_TxtFree.text = $"{Define.DAILY_GEM_PROVIDE_LIMIT - PlayerInfoMgr.data.shoppingData.dailyTotalGemProvideCount}/{Define.DAILY_GEM_PROVIDE_LIMIT}";
                        m_TxtBtnFree.text = DateFormatHelper.FormatTime(m_LeftSec);
                        m_TxtBtnFree.color = Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
                        if (m_FreeTimer != -1)
                            Timer.S.Cancel(m_FreeTimer);
                        m_FreeTimer = Timer.S.Post2Really(OnFreeTimerTick, 1, m_LeftSec);
                    }
                }
                //恢复成普通购买
                else
                {
                    if (m_ObjMask)
                    {
                        m_ObjMask.SetActive(true);
                    }
                    m_BtnFree.gameObject.SetActive(false);
                    m_TxtFree.gameObject.SetActive(false);
                    m_BtnPurchase.gameObject.SetActive(false);
                }
            }
            else
            {
                m_BtnFree.gameObject.SetActive(false);
                m_TxtFree.gameObject.SetActive(false);
                m_BtnPurchase.gameObject.SetActive(true);
                if (m_ObjMask)
                {
                    m_ObjMask.SetActive(false);
                }
            }
        }

        protected void OnFreeTimerTick(int count)
        {
            if (m_TxtFree == null)
                return;
            m_LeftSec -= 1;
            if (m_LeftSec > 0)
            {
                m_TxtBtnFree.text =
                    DateFormatHelper.FormatTime(PlayerInfoMgr.data.shoppingData.GetShopFreeBonusLeftTime());
            }
            else
            {
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }
        }
    }
}