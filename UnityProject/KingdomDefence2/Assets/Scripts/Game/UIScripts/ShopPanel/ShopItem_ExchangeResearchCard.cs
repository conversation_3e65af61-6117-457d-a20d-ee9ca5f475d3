using Qarth;
using UnityEngine;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class ShopItem_ExchangeResearchCard : ShopItem_Exchange
    {
        [SerializeField] private GameObject m_ObjMask;
        public override void OnInit(AbstractPanel panel)
        {
            base.OnInit(panel);
        }
        public override void OnOpen()
        {
            base.OnOpen();
            EventSystem.S.Register(EventID.OnPackItemStack, OnPackItemStack);
            UpdateInfo(0);
            CheckFreeState();
        }
        protected override void OnClickFree()
        {
            if (m_Conf == null)
                return;

            AdsPlayMgr.S.PlayRewardAd("FreeResearchCard", (clicked) =>
            {
                IapRewardMgr.S.GetReward(m_Conf.productId, true, true);
                PlayerInfoMgr.data.shoppingData.SetFreeResearchCardTime();
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }, null, m_BtnFree.transform.position);
        }
        public override void OnClose()
        {
            base.OnClose();
            EventSystem.S.UnRegister(EventID.OnPackItemStack, OnPackItemStack);
        }
        void OnPackItemStack(int key, params object[] args)
        {
            if (args != null && args.Length > 2)
            {
                if ((int)args[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum)args[1];
                    if (type == ItemTypeEnum.AdTicket)
                    {
                        CheckFreeState();
                    }
                }
            }
        }
        protected override void CheckFreeState()
        {
            if (m_BtnFree == null || m_TxtFree == null)
                return;

            if (m_CanFree)
            {
                //判断免费次数是否已经被耗尽
                if (PlayerInfoMgr.data.shoppingData.dailyTotalResearchCardProvideCount < Define.DAILY_RESEARCHCARD_PROVIDE_LIMIT)
                {
                    if (m_ObjMask)
                    {
                        m_ObjMask.SetActive(false);
                    }
                    m_LeftSec = PlayerInfoMgr.data.shoppingData.GetShopAdResearchCardLeftTime();
                    m_BtnFree.gameObject.SetActive(true);
                    m_BtnFree.interactable = m_LeftSec <= 0;
                    m_BtnPurchase.gameObject.SetActive(false);
                    if (m_LeftSec <= 0)
                    {
                        m_TxtFree.gameObject.SetActive(false);
                        m_TxtBtnFree.text = PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdTicket) > 0 ? string.Format("(t) {0}", TDLanguageTable.Get("EquipmentShop_Free")) : string.Format("(ad) {0}", TDLanguageTable.Get("EquipmentShop_Free"));
                        m_TxtBtnFree.color = Color.white;
                    }
                    else
                    {
                        m_TxtFree.gameObject.SetActive(true);
                        m_TxtFree.text = $"{Define.DAILY_RESEARCHCARD_PROVIDE_LIMIT - PlayerInfoMgr.data.shoppingData.dailyTotalResearchCardProvideCount}/{Define.DAILY_RESEARCHCARD_PROVIDE_LIMIT}";
                        m_TxtBtnFree.text = DateFormatHelper.FormatTime(m_LeftSec);
                        m_TxtBtnFree.color = Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
                        if (m_FreeTimer != -1)
                            Timer.S.Cancel(m_FreeTimer);
                        m_FreeTimer = Timer.S.Post2Really(OnFreeTimerTick, 1, m_LeftSec);
                    }
                }
                //恢复成普通购买
                else
                {
                    if (m_ObjMask)
                    {
                        m_ObjMask.SetActive(true);
                    }
                    m_BtnFree.gameObject.SetActive(false);
                    m_TxtFree.gameObject.SetActive(false);
                    m_BtnPurchase.gameObject.SetActive(true);
                }
            }
            else
            {
                if (m_ObjMask)
                {
                    m_ObjMask.SetActive(false);
                }
                m_BtnFree.gameObject.SetActive(false);
                m_TxtFree.gameObject.SetActive(false);
                m_BtnPurchase.gameObject.SetActive(true);
            }
        }
        protected void OnFreeTimerTick(int count)
        {
            if (m_TxtFree == null)
                return;
            m_LeftSec -= 1;
            if (m_LeftSec > 0)
            {
                m_TxtBtnFree.text =
                    DateFormatHelper.FormatTime(PlayerInfoMgr.data.shoppingData.GetShopAdResearchCardLeftTime());
            }
            else
            {
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }
        }
        void UpdateInfo(int key, params object[] args)
        {
            long rewardAmount = long.Parse(m_Conf.rewardAmount);
            m_TxtReward.text = string.Format("x{0}", rewardAmount);
        }
    }
}
