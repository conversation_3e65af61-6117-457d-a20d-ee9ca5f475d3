using System.Collections;
using System.Collections.Generic;
using Qarth;
using UnityEngine;
using UnityEngine.UI;
using System;

namespace GameWish.Game
{
    public class ShopItem_EquipMarketRefresh : ShopItem
    {
        [SerializeField] private Text m_TxtRefreshTime;
        [SerializeField] private Button m_BtnRefresh;
        [SerializeField] private Text m_TxtRefresh;
        [SerializeField] private Text m_TxtRefreshNum;
        private ShopEquipmentMarketRecorder m_Data;
        private int m_Timer;
        public override void OnInit(AbstractPanel panel)
        {
            base.OnInit(panel);
            m_Data = PlayerInfoMgr.data.shoppingData.GetShopEquipmentMarketRecorder();
        }
        public override void OnOpen()
        {
            base.OnOpen();
            m_BtnRefresh.onClick.AddListener(DoClickRefresh);
            EventSystem.S.Register(EventID.OnUpdateRefreshEquipMarketNum, OnUpdateRefreshEquipMarketNum);
            OnUpdateRefreshEquipMarketNum(0);
            RestartTimer();
        }
        void DoClickRefresh()
        {
            if (m_Data.IsRefreshAvaiable())
            {
                AdsPlayMgr.S.PlayRewardAd("RefreshEquipMarket", (c) =>
                {
                    m_Data.UpdateRefreshNum(1);
                    m_Data.ResetItem();
                }, null, m_BtnRefresh.transform.position);
            }
        }
        void OnUpdateRefreshEquipMarketNum(int key, params object[] para)
        {
            bool isAvaiable = m_Data.IsRefreshAvaiable();
            m_BtnRefresh.interactable = isAvaiable;
            m_TxtRefresh.text = isAvaiable ? TDLanguageTable.Get("ShopEquip_BtnRefresh") : TDLanguageTable.Get("ShopEquip_BtnRefreshEmpty");
            m_TxtRefreshNum.text = string.Format("({0}/{1})", Define.EQUIP_MARKET_REFRESHNUM - m_Data.dailyRefreshNum, Define.EQUIP_MARKET_REFRESHNUM);
        }
        public override void OnClose()
        {
            base.OnClose();
            m_BtnRefresh.onClick.RemoveAllListeners();
            EventSystem.S.UnRegister(EventID.OnUpdateRefreshEquipMarketNum, OnUpdateRefreshEquipMarketNum);
            Timer.S.Cancel(m_Timer);
        }
        void RestartTimer()
        {
            Timer.S.Cancel(m_Timer);
            DateTime now = DateTime.Now;
            DateTime next = now.AddDays(1).Date;
            int sec = (int)(next - now).TotalSeconds;
            m_TxtRefreshTime.text = UtilTools.FormatSecOutThree(sec, true, true);
            m_Timer = Timer.S.Post2Really((c) =>
            {
                sec--;
                m_TxtRefreshTime.text = UtilTools.FormatSecOutThree(sec, true, true);
                if (sec <= 0)
                {
                    RestartTimer();
                }
            }, 1, -1);
        }
    }
}
