using System.Collections.Generic;
using Qarth;
using UnityEngine;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class ShopItem_MixDrawBox : ShopItem
    {
        [SerializeField] protected Button m_BtnHint;
        [SerializeField] protected Button m_BtnFree;
        [SerializeField] protected Text m_TxtFree;
        [SerializeField] protected Text m_TxtGet;

        [SerializeField] protected Button m_BtnOpenOne;
        [SerializeField] protected Button m_BtnOpenTen;
        [SerializeField] protected Text m_TxtBtnOne;
        [SerializeField] protected Text m_TxtBtnTen;
        [SerializeField] protected Text m_TxtBtnOneOpen;
        [SerializeField] protected Text m_TxtBtnOpenTen;

        protected int m_LeftSec;
        protected int m_FreeTimer = -1;

        private ShopDrawMixChestInfoRecorder m_Data;

        public override void OnInit(AbstractPanel panel)
        {
            base.OnInit(panel);
            m_TxtBtnOneOpen.text = string.Format("{0} 1", TDLanguageTable.Get("ShopEquip_EquipOpenBtn"));
            m_TxtBtnOpenTen.text = string.Format("{0} 10", TDLanguageTable.Get("ShopEquip_EquipOpenBtn"));
            m_TxtGet.text = string.Format("{0}  (16)x2", TDLanguageTable.Get("Get"));
            
            m_Data = PlayerInfoMgr.data.shoppingData.GetMixDrawInfo();
        }

        public override void OnOpen()
        {
            base.OnOpen();
            m_BtnHint.onClick.AddListener(OnClickHint);
            m_BtnOpenOne.onClick.AddListener(OnClickBtnOne);
            m_BtnOpenTen.onClick.AddListener(OnClickBtnTen);
            m_BtnFree.onClick.AddListener(OnClickFree);
            CheckBtnState();
            CheckFreeState();
            EventSystem.S.Register(EventID.OnPackItemStack, OnPackItemStack);
        }

        public override void OnClose()
        {
            base.OnClose();

            m_BtnHint.onClick.RemoveAllListeners();
            m_BtnFree.onClick.RemoveAllListeners();
            m_BtnOpenOne.onClick.RemoveAllListeners();
            m_BtnOpenTen.onClick.RemoveAllListeners();
            EventSystem.S.UnRegister(EventID.OnPackItemStack, OnPackItemStack);

            if (m_FreeTimer > 0)
                Timer.S.Cancel(m_FreeTimer);
        }

        void OnPackItemStack(int key, params object[] args)
        {
            if (args != null && args.Length > 2)
            {
                if ((int) args[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum) args[1];
                    if (type == ItemTypeEnum.Gem || type == ItemTypeEnum.AdvanceEquipKey)
                    {
                        CheckBtnState();
                    }
                }
            }
        }

        protected void CheckBtnState()
        {
            //btn one
            UpdateDrawBtnState(ref m_TxtBtnOne, 1);
            //btn ten
            UpdateDrawBtnState(ref m_TxtBtnTen, 10);
        }

        protected virtual void UpdateDrawBtnState(ref Text txtComponent, int count)
        {
            if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdvanceEquipKey) >= count)
            {
                txtComponent.text = string.Format("({0}) {1}/{2}", (int) ItemTypeEnum.AdvanceEquipKey,
                    PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdvanceEquipKey), count);
            }
            else
            {
                var cost = Define.MIX_BOX_DRAW_COST_GEM_10;
                if (count == 1)
                    cost = Define.MIX_BOX_DRAW_COST_GEM_1;

                txtComponent.text = string.Format("({0}) {1}", (int) ItemTypeEnum.Gem, cost);
                // txtComponent.text = string.Format("({0})<color=#fe4042> {1}</color>/{2}", (int) ItemTypeEnum.AdvanceEquipKey,
                //     PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdvanceEquipKey), count);
            }
        }

        protected virtual void OnClickFree()
        {
            m_LeftSec = m_Data.GetFreeBonusLeftTime();
            if (m_LeftSec <= 0)
            {
                AdsPlayMgr.S.PlayRewardAd("drawMixBox", (c) =>
                {
                    m_Data.SetFreeTime(Define.FREE_MIX_DRAW_DURATION);
                    CheckFreeState();
                    // UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Treasure,
                    //     ItemTypeEnum.AdvanceEquipKey, 1, 1);
                    var reward = RewardInfo.Allocate();
                    reward.rewardType = ItemTypeEnum.AdvanceEquipKey;
                    reward.rewardAmount = 2;

                    UIMgr.S.OpenPanel(UIID.RewardPanel, new List<RewardInfo>() { reward });
                    
                    CheckBtnState();
                }, null, m_BtnFree.transform.position);
            }
        }

        protected virtual void OnClickBtnOne()
        {
            if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdvanceEquipKey) >= 1)
            {
                PackMgr.S.AddPlayerItem(-1, ItemTypeEnum.AdvanceEquipKey, "drawBox");
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Treasure,
                    ItemTypeEnum.AdvanceEquipKey, 1, 1);
                CheckBtnState();
            }
            else if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.MIX_BOX_DRAW_COST_GEM_1)
            {
                PackMgr.S.AddPlayerItem(-Define.MIX_BOX_DRAW_COST_GEM_1, ItemTypeEnum.Gem, "drawBox");
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Treasure,
                    ItemTypeEnum.Gem, Define.MIX_BOX_DRAW_COST_GEM_1, 1);
                CheckBtnState();
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        protected virtual void OnClickBtnTen()
        {
            if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.AdvanceEquipKey) >= 10)
            {
                PackMgr.S.AddPlayerItem(-10, ItemTypeEnum.AdvanceEquipKey, "drawBox");
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Treasure,
                    ItemTypeEnum.AdvanceEquipKey, 10, 10);
                CheckBtnState();
            }
            else if (PackMgr.S.GetPlayerPackItemCurrent(ItemTypeEnum.Gem) >= Define.MIX_BOX_DRAW_COST_GEM_10)
            {
                PackMgr.S.AddPlayerItem(-Define.MIX_BOX_DRAW_COST_GEM_10, ItemTypeEnum.Gem, "drawBox");
                UIMgr.S.OpenPanel(UIID.OpenBoxPanel, OpenBoxType.Treasure,
                    ItemTypeEnum.Gem, Define.MIX_BOX_DRAW_COST_GEM_10, 10);
                CheckBtnState();
            }
            else
            {
                FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
            }
        }

        protected virtual void CheckFreeState()
        {
            if (m_BtnFree == null || m_TxtFree == null)
                return;

            m_LeftSec = m_Data.GetFreeBonusLeftTime();
            m_BtnFree.interactable = m_LeftSec <= 0;

            if (m_LeftSec <= 0)
            {
                m_TxtFree.gameObject.SetActive(false);
                m_TxtGet.gameObject.SetActive(true);
            }
            else
            {
                m_TxtGet.gameObject.SetActive(false);
                m_TxtFree.gameObject.SetActive(true);
                m_TxtFree.text = DateFormatHelper.FormatTime(m_LeftSec);
                if (m_FreeTimer > 0)
                    Timer.S.Cancel(m_FreeTimer);
                m_FreeTimer = Timer.S.Post2Really(OnFreeTimerTick, 1, m_LeftSec);
            }
        }

        protected void OnFreeTimerTick(int count)
        {
            if (m_TxtFree == null)
                return;
            m_LeftSec -= 1;
            if (m_LeftSec > 0)
                m_TxtFree.text = DateFormatHelper.FormatTime(m_LeftSec);
            else
            {
                CheckFreeState();
                EventSystem.S.Send(EventID.OnUpdateShopFree);
            }
        }

        void OnClickHint()
        {
            UIMgr.S.OpenPanel(UIID.DrawBoxProbilityPanel);
        }
    }
}