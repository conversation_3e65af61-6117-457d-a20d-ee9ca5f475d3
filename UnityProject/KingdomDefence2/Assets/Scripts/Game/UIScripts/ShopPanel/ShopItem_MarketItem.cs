using System.Collections;
using System.Collections.Generic;
using Game.Tool;
using Qarth;
using UnityEngine;
using UnityEngine.UI;

namespace GameWish.Game
{
    public class ShopItem_MarketItem : ShopItem
    {
        [SerializeField] private int m_ItemId;
        [SerializeField] private Button m_BtnBuy;
        [SerializeField] private Image m_ImgBg;
        [SerializeField] private Image m_ImgIcon;
        [SerializeField] private Text m_TxtRewradName;
        [SerializeField] private Text m_TxtCost;
        [SerializeField] private Transform m_TransMask;
        private ShopEquipmentMarketItemRecorder m_Data;
        private TDEquipmentMarketConf m_MarketConf;
        private List<RewardInfo> m_LstRewards = new List<RewardInfo>();

        public override void OnInit(AbstractPanel panel)
        {
            base.OnInit(panel);
            m_BtnBuy.onClick.AddListener(DoClickBuy);
        }

        public override void OnOpen()
        {
            base.OnOpen();
            m_Data = PlayerInfoMgr.data.shoppingData.GetShopEquipmentMarketItemById(m_ItemId);
            UpdateInfo();
            UpdateState();
            EventSystem.S.Register(EventID.OnRefreshEquipMarketItem, OnRefreshEquipMarketItem);
            EventSystem.S.Register(EventID.OnBuyEquipMarketItem, OnBuyEquipMarketItem);
            EventSystem.S.Register(EventID.OnPackItemStack, OnPackItemStack);
        }
        
        public override void OnClose()
        {
            base.OnClose();
            EventSystem.S.UnRegister(EventID.OnRefreshEquipMarketItem, OnRefreshEquipMarketItem);
            EventSystem.S.UnRegister(EventID.OnBuyEquipMarketItem, OnBuyEquipMarketItem);
            EventSystem.S.UnRegister(EventID.OnPackItemStack, OnPackItemStack);
        }

        void UpdateInfo()
        {
            if (m_Data.confId > 0)
            {
                m_MarketConf = TDEquipmentMarketConfTable.GetData(m_Data.confId);
                switch (m_MarketConf.rewardTypeEnum)
                {
                    case ItemTypeEnum.Treasure:
                        var treasure = TDTreasureConfTable.GetData(m_Data.rewardIndex);
                        if (treasure != null)
                        {
                            m_HoldingPanel.FindSpriteAsync(treasure.icon + "_suipian", (spr) =>
                            {
                                m_ImgIcon.sprite = spr;
                                m_ImgIcon.SetNativeSizeMaxSize(135);
                            }, true);
                            
                            m_TxtRewradName.text = string.Format("{1}x{0}", m_MarketConf.rewardNum, TDLanguageTable.Get(treasure.name));
                            
                            m_HoldingPanel.FindSpriteAsync(
                                HeroUIUtils.GetEquipmentMarketShopBGName(m_MarketConf.quality - 1),
                                (s) => { m_ImgBg.sprite = s; }, true);

                        }
                        break;
                    default:
                        TDItemConfTable.GetItemIcon(m_MarketConf.rewardTypeEnum, (spr) =>
                        {
                            m_ImgIcon.sprite = spr;
                            m_ImgIcon.SetNativeSizeMaxSize(125);
                        });
                        m_TxtRewradName.text = string.Format("x{0}", m_MarketConf.rewardNum);
                        m_HoldingPanel.FindSpriteAsync(
                            HeroUIUtils.GetEquipmentMarketShopBGName(m_MarketConf.quality),
                            (s) => { m_ImgBg.sprite = s; }, true);

                        break;
                }
            }
            else
            {
                m_MarketConf = TDEquipmentMarketConfTable.GetRandomSlotConfByWeight(m_Data.id);
                switch (m_MarketConf.rewardTypeEnum)
                {
                    case ItemTypeEnum.Treasure:
                        var treasure = TDTreasureConfTable.GetRandomQualityConf(m_MarketConf.quality);
                        if (treasure != null)
                        {
                            m_Data.SetRewardInfo(m_ItemId, m_MarketConf.id, treasure.id);
                            m_HoldingPanel.FindSpriteAsync(treasure.icon, (spr) =>
                            {
                                m_ImgIcon.sprite = spr;
                                m_ImgIcon.SetNativeSize();
                            }, true);
                            m_TxtRewradName.text = string.Format("{1}x{0}", m_MarketConf.rewardNum, TDLanguageTable.Get(treasure.name));
                            
                            m_HoldingPanel.FindSpriteAsync(
                                HeroUIUtils.GetEquipmentMarketShopBGName(m_MarketConf.quality -1),
                                (s) => { m_ImgBg.sprite = s; }, true);

                        }

                        break;
                    default:
                        m_Data.SetRewardInfo(m_ItemId, m_MarketConf.id, -1);
                        TDItemConfTable.GetItemIcon(m_MarketConf.rewardTypeEnum, (spr) =>
                        {
                            m_ImgIcon.sprite = spr;
                            m_ImgIcon.SetNativeSize();
                        });
                        m_TxtRewradName.text = string.Format("x{0}", m_MarketConf.rewardNum);
                        m_HoldingPanel.FindSpriteAsync(
                            HeroUIUtils.GetEquipmentMarketShopBGName(m_MarketConf.quality),
                            (s) => { m_ImgBg.sprite = s; }, true);

                        break;
                }
            }
            
            UpdateCost();
        }

        void UpdateState()
        {
            m_TransMask.gameObject.SetActive(m_Data.isBuy);
        }

        void DoClickBuy()
        {
            if (!m_Data.isBuy)
            {
                if (m_MarketConf.costType == (int)ItemTypeEnum.Ad)
                {
                    AdsPlayMgr.S.PlayRewardAd("GetEquipMarketReward_" + m_MarketConf.id, (c) => { GetReward(); }, 
                        null, m_BtnBuy.transform.position);
                }
                else
                {
                    if (PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)m_MarketConf.costType) >=
                        m_MarketConf.costNum)
                    {
                        PackMgr.S.AddPlayerItem(-m_MarketConf.costNum, (ItemTypeEnum)m_MarketConf.costType, "buyMarket");
                        GetReward();
                    }
                    else
                    {
                        FloatMessage.S.ShowMsg(TDLanguageTable.Get("NotEnoughRes"));
                    }
                }
            }
        }

        void UpdateCost()
        {
            if (m_MarketConf.costType == (int)ItemTypeEnum.Ad)
            {
                m_TxtCost.color = Color.white;
                m_TxtCost.text = string.Format("({0}){1}", m_MarketConf.costType,
                    TDLanguageTable.Get("EquipmentShop_Free"));
            }
            else
            {
                if(PackMgr.S.GetPlayerPackItemCurrent((ItemTypeEnum)m_MarketConf.costType) >= m_MarketConf.costNum)
                {
                    m_TxtCost.color = Color.white;
                }
                else
                {
                    m_TxtCost.color = Helper.String2ColorHex(Define.RES_NOTENOUGH_COLOR);
                }
                
                m_TxtCost.text = string.Format("({0}){1}", m_MarketConf.costType,
                    m_MarketConf.costNum);
            }
        }

        void GetReward()
        {
            if (m_MarketConf == null || m_Data == null)
                return;

            m_LstRewards.Clear();
            m_Data.DoBuy();

            RewardInfo reward = RewardInfo.Allocate();
            reward.rewardType = m_MarketConf.rewardTypeEnum;
            reward.rewardAmount = m_MarketConf.rewardNum;
            if (m_MarketConf.rewardTypeEnum == ItemTypeEnum.Treasure)
            {
                reward.rewardValue = m_Data.rewardIndex;
            }

            m_LstRewards.Add(reward);
            UIMgr.S.OpenPanel(UIID.RewardPanel, m_LstRewards);
        }


        void OnRefreshEquipMarketItem(int key, params object[] para)
        {
            UpdateInfo();
            UpdateState();
        }

        void OnBuyEquipMarketItem(int key, params object[] para)
        {
            int id = int.Parse(para[0].ToString());
            if (id == m_Data.id)
            {
                UpdateState();
                UpdateCost();
            }
        }
        
        void OnPackItemStack(int key, params object[] args)
        {
            if (args != null && args.Length > 2)
            {
                if ((int)args[0] == Define.PLAYER_ID)
                {
                    var type = (ItemTypeEnum)args[1];
                    if (type == ItemTypeEnum.Gem)
                    {
                        UpdateCost();
                    }
                }
            }
        }


    }
}