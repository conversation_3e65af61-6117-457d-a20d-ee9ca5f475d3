//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;

namespace Qarth
{
    public static partial class TDAdjustEvtTokenTable
    {
        private static TDTableMetaData m_MetaData = new TDTableMetaData(TDAdjustEvtTokenTable.Parse, "adjust_evt_token");
        public static TDTableMetaData metaData
        {
            get { return m_MetaData; }
        }
        
        private static Dictionary<string, TDAdjustEvtToken> m_DataCache = new Dictionary<string, TDAdjustEvtToken>();
        private static List<TDAdjustEvtToken> m_DataList = new List<TDAdjustEvtToken >();
        private static byte[] m_FileDataCache;
        public static byte[] fileDataCache
        {
            get { return m_FileDataCache; }
        }
        
        public static void Parse(byte[] fileData)
        {
            m_DataCache.Clear();
            m_DataList.Clear();
            m_FileDataCache = fileData;
            DataStreamReader dataR = new DataStreamReader(fileData);
            int rowCount = dataR.GetRowCount();
            int[] fieldIndex = dataR.GetFieldIndex(TDAdjustEvtToken.GetFieldHeadIndex());
    #if (UNITY_STANDALONE_WIN) || UNITY_EDITOR || UNITY_STANDALONE_OSX
            dataR.CheckFieldMatch(TDAdjustEvtToken.GetFieldHeadIndex(), "AdjustEvtTokenTable");
    #endif
            for (int i = 0; i < rowCount; ++i)
            {
                TDAdjustEvtToken memberInstance = new TDAdjustEvtToken();
                memberInstance.ReadRow(dataR, fieldIndex);
                OnAddRow(memberInstance);
                memberInstance.Reset();
                CompleteRowAdd(memberInstance);
            }
            Log.i(string.Format("Parse Success TDAdjustEvtToken"));
        }

        private static void OnAddRow(TDAdjustEvtToken memberInstance)
        {
            string key = memberInstance.id;
            if (m_DataCache.ContainsKey(key))
            {
                Log.e(string.Format("Invaild,  TDAdjustEvtTokenTable Id already exists {0}", key));
            }
            else
            {
                m_DataCache.Add(key, memberInstance);
                m_DataList.Add(memberInstance);
            }
        }    
        
        public static void Reload(byte[] fileData)
        {
            Parse(fileData);
        }

        public static int count
        {
            get 
            {
                return m_DataCache.Count;
            }
        }

        public static List<TDAdjustEvtToken> dataList
        {
            get 
            {
                return m_DataList;
            }    
        }

        public static TDAdjustEvtToken GetData(string key)
        {
            if (m_DataCache.ContainsKey(key))
            {
                return m_DataCache[key];
            }
            else
            {
                Log.w(string.Format("Can't find key {0} in TDAdjustEvtToken", key));
                return null;
            }
        }
    }
}//namespace LR